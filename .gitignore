# EasyYTDLP - Git Ignore File

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Downloaded videos (for testing)
*.mp4
*.mkv
*.webm
*.m4a
*.mp3
*.part
*.ytdl
*.temp
*.tmp
*.frag*

# Logs
*.log

# Build artifacts
EasyYTDLP.exe
EasyYTDLP/

# Temporary files
temp/
tmp/
test_*
