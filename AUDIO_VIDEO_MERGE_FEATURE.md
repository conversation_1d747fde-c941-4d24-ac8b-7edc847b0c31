# Audio + Video Merge Feature

## Overview

EasyYTDLP now includes a powerful **Audio + Video Merge** feature that allows you to combine separate audio and video files into a single MKV file. This is perfect for:

- Combining downloaded video-only and audio-only files
- Adding custom audio tracks to videos
- Replacing poor quality audio with high-quality versions
- Creating videos with different language audio tracks
- Merging content from different sources

## How to Use

### 1. Access the Feature
- Launch EasyYTDLP
- Look for the **"🎬 Merge A+V"** button in the control panel
- Click the button to start the merge process

### 2. Select Files
The merge process involves three simple steps:

1. **Select Video File**: Choose your video file (supports .mkv, .mp4, .webm, .avi, .mov, .flv)
2. **Select Audio File**: Choose your audio file (supports .mp3, .m4a, .aac, .wav, .flac, .ogg, .opus)
3. **Choose Output Location**: Specify where to save the merged file (defaults to .mkv format)

### 3. Monitor Progress
- The merge process runs in the background
- Progress is shown in the download log
- You'll see confirmation when the merge completes
- A success dialog will appear when finished

## Technical Details

### FFmpeg Command Used
```bash
ffmpeg -i video.mp4 -i audio.mp3 \
  -c:v copy \                    # Copy video without re-encoding (fast)
  -c:a aac \                     # Convert audio to AAC (compatible)
  -map 0:v:0 \                   # Map first video stream
  -map 1:a:0 \                   # Map first audio stream
  -shortest \                    # End when shortest stream ends
  -avoid_negative_ts make_zero \ # Fix timestamp issues
  -fflags +genpts \              # Generate presentation timestamps
  -movflags +faststart \         # Optimize for streaming
  -y output.mkv                  # Overwrite output file
```

### Key Features
- **Fast Processing**: Video is copied without re-encoding
- **Audio Conversion**: Audio is converted to AAC for maximum compatibility
- **Stream Mapping**: Precisely selects which video and audio streams to use
- **Timestamp Fixing**: Handles timing issues automatically
- **Optimized Output**: Creates streaming-optimized MKV files

## Supported Formats

### Input Video Formats
- **MKV** (.mkv) - Matroska Video
- **MP4** (.mp4) - MPEG-4 Video
- **WebM** (.webm) - WebM Video
- **AVI** (.avi) - Audio Video Interleave
- **MOV** (.mov) - QuickTime Movie
- **FLV** (.flv) - Flash Video

### Input Audio Formats
- **MP3** (.mp3) - MPEG Audio Layer 3
- **M4A** (.m4a) - MPEG-4 Audio
- **AAC** (.aac) - Advanced Audio Coding
- **WAV** (.wav) - Waveform Audio
- **FLAC** (.flac) - Free Lossless Audio Codec
- **OGG** (.ogg) - Ogg Vorbis
- **OPUS** (.opus) - Opus Audio

### Output Format
- **MKV** (.mkv) - Default output format (recommended)
- **MP4** (.mp4) - Alternative output format

## Use Cases

### 1. YouTube Downloads
When downloading from YouTube, you might get:
- High-quality video file (video-only)
- High-quality audio file (audio-only)

Use the merge feature to combine them into one playable file.

### 2. Custom Audio Tracks
- Replace poor quality audio with high-quality version
- Add different language audio tracks
- Combine video with custom music or narration

### 3. Content Creation
- Merge screen recordings with separate audio commentary
- Combine video clips with custom soundtracks
- Create videos with multiple audio sources

### 4. Format Conversion
- Convert various video/audio combinations to standardized MKV
- Ensure compatibility across different players and devices

## Quality and Performance

### Video Quality
- **No Quality Loss**: Video stream is copied without re-encoding
- **Original Resolution**: Maintains original video resolution and quality
- **Fast Processing**: No video processing time required

### Audio Quality
- **AAC Encoding**: Converts audio to high-quality AAC format
- **Bitrate Preservation**: Maintains reasonable audio quality
- **Compatibility**: AAC works on all modern devices and players

### Processing Speed
- **Fast**: Video copying is nearly instantaneous
- **Efficient**: Only audio needs processing/conversion
- **Background Processing**: Doesn't freeze the GUI during merge

## Troubleshooting

### Common Issues

**"FFmpeg not found"**
- Install FFmpeg from https://ffmpeg.org/download.html
- Ensure FFmpeg is in your system PATH
- Restart EasyYTDLP after installing FFmpeg

**"Merge failed"**
- Check that both input files are valid and not corrupted
- Ensure you have write permissions to the output directory
- Try different input files to isolate the issue

**"Output file is empty"**
- Input files may be corrupted or incompatible
- Check that video file actually contains video streams
- Verify audio file contains audio streams

**"Process timed out"**
- Large files may take longer to process
- Close other applications to free up system resources
- Try with smaller test files first

### File Compatibility
- **Video codecs**: H.264, H.265, VP8, VP9, AV1 supported
- **Audio codecs**: Most common audio formats supported
- **Container formats**: Most standard containers work

## Tips for Best Results

### File Selection
1. **Match Duration**: Use audio and video files of similar length
2. **Quality Balance**: Don't use very high-quality audio with low-quality video
3. **Format Compatibility**: Stick to common formats for best results

### Output Settings
1. **Use MKV**: MKV format provides best compatibility and features
2. **Choose Good Location**: Save to a location with plenty of free space
3. **Descriptive Names**: Use clear filenames for merged outputs

### Performance
1. **Close Other Apps**: Free up system resources during merge
2. **Use SSD**: Faster storage improves processing speed
3. **Sufficient RAM**: Ensure adequate memory for large files

## Examples

### Example 1: YouTube Download Merge
```
Video: my_video.mp4 (1080p, no audio)
Audio: my_audio.m4a (high quality)
Output: my_video_merged.mkv (1080p with audio)
```

### Example 2: Custom Soundtrack
```
Video: gameplay.mp4 (with original audio)
Audio: custom_music.mp3 (background music)
Output: gameplay_with_music.mkv (video + custom audio)
```

### Example 3: Language Replacement
```
Video: movie.mkv (English audio)
Audio: movie_spanish.mp3 (Spanish audio)
Output: movie_spanish.mkv (video + Spanish audio)
```

## Integration with EasyYTDLP

The merge feature integrates seamlessly with EasyYTDLP's existing functionality:

- **Same Interface**: Uses familiar file dialogs and progress logging
- **FFmpeg Integration**: Leverages existing FFmpeg process management
- **Error Handling**: Consistent error reporting and recovery
- **Background Processing**: Non-blocking operation like downloads

This feature makes EasyYTDLP a complete solution for both downloading and post-processing video content!
