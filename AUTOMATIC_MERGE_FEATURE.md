# Automatic Video + Audio Merge Feature

## Overview

EasyYTDLP now includes **automatic video+audio merging** that detects when yt-dlp downloads separate video and audio files and automatically combines them into a single MKV file. This eliminates the need for manual merging and ensures you always get complete video files with audio.

## How It Works

### 1. Enhanced yt-dlp Commands
EasyYTDLP now uses optimized yt-dlp commands that prioritize automatic merging:

```bash
yt-dlp [URL] \
  --merge-output-format mkv \           # Force MKV output
  --postprocessor-args "ffmpeg:-c:v copy -c:a aac" \  # Optimal merge settings
  --prefer-ffmpeg \                     # Use FFmpeg for merging
  --no-keep-video \                     # Don't keep separate video file
  --no-keep-audio                       # Don't keep separate audio file
```

### 2. Automatic Detection
After each download, EasyYTDLP automatically:

1. **Scans the output directory** for video and audio files with matching names
2. **Detects separate files** (e.g., `video.mp4` + `video.m4a`)
3. **Automatically merges them** using FFmpeg
4. **Cleans up original files** after successful merge
5. **Creates a single MKV file** with both video and audio

### 3. Smart File Matching
The system looks for files with the same base name:

**Example Patterns Detected:**
- `my_video.mp4` + `my_video.m4a` → `my_video_merged.mkv`
- `stream.webm` + `stream.opus` → `stream_merged.mkv`
- `download.mkv` + `download.aac` → `download_merged.mkv`

## Supported File Types

### Video Files
- **MP4** (.mp4) - Most common format
- **MKV** (.mkv) - Matroska container
- **WebM** (.webm) - Web video format
- **AVI** (.avi) - Legacy format

### Audio Files
- **M4A** (.m4a) - iTunes audio format
- **MP3** (.mp3) - Common audio format
- **AAC** (.aac) - Advanced audio coding
- **OPUS** (.opus) - Modern audio codec
- **OGG** (.ogg) - Open source audio

## User Experience

### What You See
When automatic merging occurs, you'll see these messages in the download log:

```
🔍 Found separate video and audio files:
  📹 Video: my_video.mp4
  🔊 Audio: my_video.m4a
🎬 Attempting automatic video+audio merge...
🎬 Starting audio + video merge...
📹 Video: my_video.mp4
🔊 Audio: my_video.m4a
🔧 FFmpeg merge completed with exit code: 0
✅ Audio + Video merge successful!
📁 Output file: my_video_merged.mkv
✅ Automatic merge successful!
🗑️ Cleaning up original separate files...
✅ Original separate files deleted
```

### What You Get
- **Single MKV file** with both video and audio
- **No separate files** cluttering your download folder
- **Optimal quality** - video copied without re-encoding
- **Compatible audio** - converted to AAC for universal playback

## Technical Details

### Merge Process
1. **Detection**: Scans for video/audio file pairs after download
2. **Validation**: Checks file sizes and formats
3. **Merging**: Uses FFmpeg with optimal settings:
   - Video: Stream copy (no quality loss)
   - Audio: AAC conversion (universal compatibility)
   - Container: MKV (best feature support)
4. **Cleanup**: Removes original separate files
5. **Verification**: Confirms successful merge

### FFmpeg Command Used
```bash
ffmpeg -i video.mp4 -i audio.m4a \
  -c:v copy \                    # Copy video (fast, no quality loss)
  -c:a aac \                     # Convert audio to AAC
  -map 0:v:0 \                   # Map video stream
  -map 1:a:0 \                   # Map audio stream
  -shortest \                    # Match shortest duration
  -avoid_negative_ts make_zero \ # Fix timing issues
  -fflags +genpts \              # Generate timestamps
  -movflags +faststart \         # Optimize for streaming
  -y output_merged.mkv           # Overwrite if exists
```

## Benefits

### For Users
- **No manual work** - everything happens automatically
- **Clean downloads** - no separate files to manage
- **Universal compatibility** - MKV files play everywhere
- **Optimal quality** - no unnecessary re-encoding

### For Content Creators
- **Ready for editing** - single files are easier to work with
- **Consistent format** - all downloads in MKV format
- **Time saving** - no manual merging required
- **Reliable results** - automatic process reduces errors

## When It Activates

### Automatic Triggers
The merge process activates when:
- yt-dlp downloads separate video and audio files
- Files have matching base names in the output directory
- Both files are valid and contain data
- FFmpeg is available on the system

### When It Doesn't Activate
No merging occurs when:
- Download produces a single file (already merged)
- No matching video/audio pairs found
- Files are corrupted or empty
- FFmpeg is not installed

## Troubleshooting

### Common Issues

**"No separate files found"**
- This is normal - yt-dlp successfully merged during download
- No action needed, your video already has audio

**"Automatic merge failed"**
- Check that FFmpeg is installed and working
- Verify input files are not corrupted
- Check available disk space

**"Could not delete original files"**
- Files may be in use by another program
- Close video players or file explorers
- Original files remain but merge was successful

### Verification
To verify automatic merging is working:
1. Download a video that typically creates separate files
2. Check the download log for merge messages
3. Confirm you get a single MKV file
4. Play the file to verify audio is included

## Configuration

### Current Settings
The automatic merge feature is:
- **Always enabled** - no option to disable
- **Automatic** - requires no user intervention
- **Optimized** - uses best quality settings
- **Safe** - only deletes files after successful merge

### Future Enhancements
Potential future options:
- User choice to keep/delete original files
- Custom output filename patterns
- Different audio codec options
- Merge quality settings

## Compatibility

### Operating Systems
- **Windows** - Fully supported
- **macOS** - Supported (requires FFmpeg)
- **Linux** - Supported (requires FFmpeg)

### Requirements
- **FFmpeg** - Must be installed and accessible
- **Disk Space** - Temporary space for merge process
- **File Permissions** - Write access to output directory

## Performance

### Speed
- **Fast** - Video stream copying is nearly instantaneous
- **Efficient** - Only audio needs processing
- **Background** - Doesn't block the GUI

### Resource Usage
- **Low CPU** - Stream copying uses minimal processing
- **Moderate I/O** - Reading/writing files during merge
- **Temporary Space** - Brief additional disk usage during merge

This automatic merge feature ensures that EasyYTDLP downloads are always complete, playable files without any manual intervention required!
