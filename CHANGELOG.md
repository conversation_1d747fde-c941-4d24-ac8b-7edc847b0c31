# Changelog

All notable changes to EasyYTDLP will be documented in this file.

## [1.1.1] - 2025-06-16

### 🧹 Codebase Cleanup & Organization
- **Removed Test Files**: Cleaned up all test files and temporary directories
- **Organized Structure**: Created proper directory structure with `utils/` and `docs/`
- **Utility Scripts**: Moved build and download utilities to `utils/` directory
- **Documentation**: Updated README and project structure documentation
- **Clean Distribution**: Streamlined codebase for easier maintenance and distribution

### 📁 New Project Structure
- **Main Application**: Core files in root directory
- **Utils Directory**: Development and build utilities
- **Docs Directory**: Comprehensive documentation
- **Clean Root**: Only essential files in main directory

## [1.1.0] - 2025-06-15

### 🔧 Added - FFmpeg Live Stream Merging
- **Automatic Live Stream Detection**: Detects YouTube live streams automatically
- **FFmpeg Integration**: Automatic merging of partial downloads when stopped mid-stream
- **Partial File Handling**: Searches for and processes `.part`, `.ytdl`, `.temp`, and fragment files
- **Stream Copy Merging**: Uses `ffmpeg -c copy` for fast, lossless merging
- **Smart File Detection**: Identifies separate video and audio streams from interrupted downloads
- **Quality Preservation**: No re-encoding ensures original quality is maintained

### 🎯 Enhanced Features
- **Live Stream Commands**: Added `--live-from-start`, `--hls-use-mpegts`, `--keep-fragments` for live streams
- **Improved Error Handling**: Better feedback for FFmpeg availability and merge status
- **Enhanced GUI Messages**: Clear indicators for live stream processing and merge status
- **Documentation**: Comprehensive FFmpeg merging guide and command reference

### 🎮 Perfect for Content Creation
- **TikTok Ready**: Extract highlights from long live streams without downloading entire streams
- **Fast Processing**: Stream copy merging takes seconds instead of hours
- **Reliable Output**: Merged files have smooth seeking and perfect playback compatibility

### 📁 Project Organization
- **Documentation**: Moved detailed docs to `docs/` folder
- **Clean Structure**: Organized project files and removed test artifacts
- **Better README**: Updated with live stream features and project structure

## [1.0.0] - 2025-06-12

### 🚀 Initial Release
- **Optimal yt-dlp Commands**: Researched best practices for 1080p downloads
- **Smart Format Selection**: Prioritizes quality while maintaining compatibility
- **MKV Output**: Universal container format with excellent codec support
- **Progress Tracking**: Real-time download progress with elapsed time
- **Audio-Only Mode**: High-quality MP3 extraction
- **Subtitle Support**: Optional subtitle embedding
- **Error Handling**: Robust retry logic and graceful error recovery
- **Cross-Platform**: Works on Windows, Mac, and Linux
- **GUI Interface**: Clean, modern tkinter interface
- **Batch File**: Easy Windows launcher

### 🎯 Quality Features
- **True 1080p**: Smart format selection ensures real 1080p quality
- **Best Audio**: Automatic selection of highest quality audio streams
- **H.264 Preference**: Prioritizes widely compatible video codecs
- **Concurrent Downloads**: Multiple fragment downloads for speed
- **Automatic Retries**: Built-in network error recovery

### 📋 Technical Foundation
- **Python 3.9+**: Modern Python with tkinter GUI
- **yt-dlp Integration**: Latest YouTube downloading technology
- **Threading**: Non-blocking downloads with real-time updates
- **Cross-Platform**: Designed to work across operating systems
