# FFmpeg Hanging Process Fix

## Problem Description

**Symptoms:**
- FFmpeg.exe processes remain in Task Manager after downloads complete
- Processes use 0-5% CPU and consume memory
- Downloaded video files cannot be deleted or overwritten until FFmpeg is manually killed
- Log shows "fix successful" but files remain locked
- Multiple FFmpeg processes accumulate over time

**Root Causes:**
1. **Incomplete Process Cleanup**: FFmpeg processes weren't being properly terminated after completion
2. **File Handle Locks**: FFmpeg processes holding file handles even after finishing
3. **Process Group Isolation**: FFmpeg processes not properly isolated for cleanup
4. **Missing Exit Cleanup**: No cleanup when application closes
5. **Explorer Preview Locks**: Windows Explorer thumbnails can lock video files

## Solution Implemented

### 1. Enhanced FFmpeg Process Management

**Before:**
```python
# Basic process creation with minimal cleanup
process = subprocess.Popen(cmd, ...)
process.communicate(timeout=120)
self._force_kill_process(process)  # Basic cleanup
```

**After:**
```python
# Enhanced process isolation and cleanup
creation_flags = subprocess.CREATE_NEW_PROCESS_GROUP | subprocess.CREATE_BREAKAWAY_FROM_JOB
ffmpeg_process = subprocess.Popen(cmd, creationflags=creation_flags, ...)

# Multiple cleanup layers
self._force_kill_ffmpeg_process(ffmpeg_process)  # Process-specific cleanup
self._kill_all_ffmpeg_processes()  # System-wide cleanup
time.sleep(3.0)  # Extended wait for file handle release
```

### 2. New FFmpeg-Specific Cleanup Method

```python
def _force_kill_ffmpeg_process(self, process):
    """Enhanced FFmpeg-specific process termination"""
    if process and process.poll() is None:
        # Graceful termination first
        process.terminate()
        try:
            process.wait(timeout=3)
        except subprocess.TimeoutExpired:
            # Force kill if needed
            process.kill()
            process.wait(timeout=2)
        
        # Windows-specific PID cleanup
        if os.name == 'nt' and hasattr(process, 'pid'):
            os.kill(process.pid, signal.SIGTERM)
```

### 3. System-Wide FFmpeg Cleanup

```python
def _kill_all_ffmpeg_processes(self):
    """Kill any remaining FFmpeg processes system-wide"""
    if os.name == 'nt':  # Windows
        # Kill all ffmpeg.exe processes
        subprocess.run(['taskkill', '/f', '/im', 'ffmpeg.exe'], ...)
        # Also kill processes with ffmpeg in name
        subprocess.run(['taskkill', '/f', '/fi', 'IMAGENAME eq ffmpeg*'], ...)
    else:  # Unix-like
        subprocess.run(['pkill', '-f', 'ffmpeg'], ...)
```

### 4. Application Exit Cleanup

```python
def _on_closing(self):
    """Handle application closing with proper cleanup"""
    # Stop active downloads
    if self.download_manager.is_downloading:
        self.download_manager.stop_download()
    
    # Kill all FFmpeg processes
    self.download_manager._kill_all_ffmpeg_processes()
    
    # Wait for cleanup
    time.sleep(0.5)
    
    # Close application
    self.root.destroy()
```

### 5. Enhanced Finish Here Cleanup

```python
def finish_here(self):
    """Gracefully stop download with enhanced cleanup"""
    try:
        # ... existing download stopping logic ...
        self._fix_partial_download()
    finally:
        # Ensure all FFmpeg processes are cleaned up
        self._kill_all_ffmpeg_processes()
```

## Key Improvements

### Process Isolation
- **CREATE_NEW_PROCESS_GROUP**: Creates isolated process group for better control
- **CREATE_BREAKAWAY_FROM_JOB**: Prevents inheritance of job restrictions
- **Enhanced timeout handling**: Multiple timeout layers with proper fallbacks

### Multi-Layer Cleanup
1. **Process-specific cleanup**: Target the specific FFmpeg process
2. **System-wide cleanup**: Kill any remaining FFmpeg processes
3. **File handle release**: Extended waits for Windows file system
4. **Application exit cleanup**: Cleanup when app closes

### Better Error Handling
- **Timeout management**: Proper handling of FFmpeg timeouts
- **Exception safety**: Cleanup runs even if errors occur
- **Logging**: Clear feedback about cleanup operations

## Testing

Run the test script to verify the fix:

```bash
python test_ffmpeg_cleanup.py
```

**Test Coverage:**
- Normal FFmpeg completion and cleanup
- FFmpeg interruption and forced cleanup
- System-wide FFmpeg process cleanup
- Multiple concurrent FFmpeg processes

## Prevention Tips

### For Users:
1. **Avoid Explorer Preview**: Don't browse output folder during downloads
2. **Close Video Players**: Ensure no video players have files open
3. **Use CLI Alternative**: For problematic cases, use direct yt-dlp CLI

### For Developers:
1. **Always use process groups**: Isolate subprocesses for better cleanup
2. **Multiple cleanup layers**: Don't rely on single cleanup method
3. **Extended timeouts**: Allow time for Windows file handle release
4. **Exit handlers**: Always cleanup on application exit

## Batch Script Alternative

For users who prefer CLI approach, here's a reliable batch script:

```batch
@echo off
yt-dlp %1 --format "bestvideo[height<=1080]+bestaudio" --merge-output-format mkv
taskkill /IM ffmpeg.exe /F >nul 2>&1
echo FFmpeg cleanup completed
```

## Results

**Before Fix:**
- FFmpeg processes accumulate in Task Manager
- Files remain locked after downloads
- Manual Task Manager intervention required
- Poor user experience

**After Fix:**
- Automatic FFmpeg process cleanup
- Files immediately available after downloads
- No manual intervention needed
- Smooth user experience
- Reliable operation across multiple downloads

## Monitoring

To monitor FFmpeg processes:

**Windows:**
```cmd
tasklist | findstr ffmpeg
```

**Linux/Mac:**
```bash
ps aux | grep ffmpeg
```

The fix ensures these commands show no lingering FFmpeg processes after downloads complete.
