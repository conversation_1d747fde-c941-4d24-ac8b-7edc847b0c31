# EasyYTDLP - Optimal YouTube Downloader

A simple, efficient YouTube downloader built with researched yt-dlp best practices for optimal video quality and reliability.

## ✨ Features

- **Optimal Quality**: Downloads YouTube videos in true 1080p with audio merged into MKV format
- **Smart Format Selection**: Uses researched yt-dlp commands for best quality/compatibility balance
- **Multiple Quality Options**: 1080p, 720p, 480p, or Best Available
- **Audio-Only Downloads**: High-quality MP3 extraction
- **Subtitle Support**: Optional subtitle embedding
- **Progress Tracking**: Real-time download progress with time elapsed
- **Finish Here Feature**: Stop long downloads at current point and keep partial MKV file
- **🔧 Live Stream Support**: Automatic FFmpeg merging for partial live stream downloads
- **User-Friendly GUI**: Clean, modern interface
- **Reliable Downloads**: Built-in retry logic and error handling

## 🎯 Optimal Commands Used

This application uses researched optimal yt-dlp commands:

### Video Downloads (1080p MKV):
```bash
yt-dlp -f "bestvideo[height<=1080]+bestaudio/best[height<=1080]" \
       --merge-output-format mkv \
       --embed-metadata \
       --no-playlist \
       --restrict-filenames \
       --retries 10 \
       --fragment-retries 10 \
       --concurrent-fragments 4
```

### Audio Downloads (MP3):
```bash
yt-dlp -f "bestaudio/best" \
       -x --audio-format mp3 \
       --audio-quality 0 \
       --embed-metadata \
       --no-playlist \
       --restrict-filenames
```

## 📋 Requirements

- **Python 3.9+** (for running from source)
- **yt-dlp.exe** (included or download separately)
- **FFmpeg** (required for live stream merging, recommended for best compatibility)

## 📁 Project Structure

```
EasyYTDLP/
├── main.py                 # Main application
├── run_easytdlp.bat        # Windows batch launcher
├── requirements.txt        # Python dependencies
├── yt-dlp.exe             # yt-dlp executable
├── README.md              # This file
├── CHANGELOG.md           # Version history
├── docs/                  # Documentation
│   ├── COMMANDS.md        # yt-dlp command reference
│   ├── FFMPEG_MERGING.md  # FFmpeg live stream guide
│   └── QUICK_START.md     # Quick start guide
└── utils/                 # Development utilities
    ├── build_exe.py       # PyInstaller build script
    ├── download_ytdlp.py  # yt-dlp downloader utility
    └── README.md          # Utils documentation
```

## 🚀 Quick Start

### Option 1: Download Executable (Recommended)
1. Download the latest release from the releases page
2. Extract to a folder
3. Run `EasyYTDLP.exe`

### Option 2: Run from Source
1. Clone or download this repository
2. Download `yt-dlp.exe`: `python utils/download_ytdlp.py`
3. Run: `python main.py`

### Option 3: Build Your Own Executable
1. Install PyInstaller: `pip install pyinstaller`
2. Download yt-dlp.exe: `python utils/download_ytdlp.py`
3. Build executable: `python utils/build_exe.py`
4. Find your executable in the `dist/` folder

## 📥 Getting yt-dlp.exe

### Option 1: Automatic Download (Recommended)
```bash
python utils/download_ytdlp.py
```

### Option 2: Manual Download
Download the latest yt-dlp.exe from:
- **Official**: https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp.exe
- Place it in the same directory as the application

## 🎮 How to Use

1. **Enter YouTube URL**: Paste any YouTube video URL
2. **Set Filename**: Enter desired filename (or use Auto-fill)
3. **Choose Output Directory**: Select where to save the file
4. **Select Quality**: Choose video quality (1080p recommended)
5. **Options**:
   - Check "Audio Only" for MP3 extraction
   - Check "Include Subtitles" for subtitle embedding
6. **For Long Videos**: Check "Enable 'Finish Here' Mode" before starting
7. **Click "Start Download"**: Begin the download process
8. **For Long Videos**: Use "Finish Here" button to stop at current point and keep partial file

## 🔧 Quality Options

| Option | Description | Output Format |
|--------|-------------|---------------|
| **1080p** | Best video ≤1080p + best audio | MKV |
| **720p** | Best video ≤720p + best audio | MKV |
| **480p** | Best video ≤480p + best audio | MKV |
| **Best Available** | Highest quality available | MKV |
| **Audio Only** | Best audio extraction | MP3 |

## 🎯 Why These Commands Are Optimal

### Format Selection Logic:
- **`bestvideo[height<=1080]+bestaudio`**: Gets separate best video (≤1080p) and best audio
- **`/best[height<=1080]`**: Fallback to single file if merging fails
- **`--merge-output-format mkv`**: Forces MKV container (universal compatibility)

### Performance Features:
- **`--concurrent-fragments 4`**: Downloads multiple fragments simultaneously
- **`--retries 10`**: Automatic retry on failures
- **`--embed-metadata`**: Adds title, description to file
- **`--restrict-filenames`**: Cross-platform filename compatibility

### Why MKV Format:
- ✅ Supports all video/audio codecs
- ✅ Reliable merging of separate streams
- ✅ Better seeking performance
- ✅ Metadata support
- ✅ Cross-platform compatibility

## 🛠 Advanced Features

### Automatic Optimizations:
- Smart format fallbacks for maximum compatibility
- Concurrent fragment downloads for speed
- Automatic retry on network errors
- Metadata embedding for better file organization
- Filename sanitization for cross-platform use

### Download Control:
- **Stop Download**: Immediately abort download (no file saved)
- **Finish Here Mode**: Enable special mode for long videos that optimizes for partial downloads
- **Finish Here Button**: Gracefully stop and save partial MKV file (only available in Finish Here mode)
- Perfect for 10+ hour videos when you only need a portion
- Uses progressive formats to ensure partial files are properly saved and playable
- Partial files are fully playable with smooth seeking

### 🔧 Live Stream Features:
- **Automatic Detection**: Detects YouTube live streams automatically
- **Partial Download Support**: Stop live streams at any point
- **FFmpeg Auto-Merge**: Automatically merges partial video/audio files
- **Fragment Handling**: Processes DASH stream fragments seamlessly
- **Quality Preservation**: No re-encoding, maintains original quality
- **Perfect for Highlights**: Extract specific segments from long live streams

### Error Handling:
- Network timeout protection
- Automatic retry mechanisms
- Graceful fallback to lower quality if needed
- Clear error messages and logging

## 📊 Performance Tips

1. **Use 1080p setting** for best quality/speed balance
2. **Enable concurrent fragments** (automatic) for faster downloads
3. **Choose MKV format** (automatic) for best compatibility
4. **Use wired internet** for large file downloads
5. **Close other bandwidth-heavy applications** during download

## 🔍 Troubleshooting

### Common Issues:

**"yt-dlp.exe not found"**
- Download yt-dlp.exe and place it in the application folder

**"Download failed"**
- Check internet connection
- Try a different video URL
- Check if video is available in your region

**"Merge failed"**
- Install FFmpeg for better format support
- Try a different quality setting

**"FFmpeg not found"** (for live streams)
- Download FFmpeg from https://ffmpeg.org/download.html
- Add FFmpeg to your system PATH
- Restart the application

**Slow downloads**
- Check internet speed
- Try lower quality setting
- Close other applications using bandwidth

## 📝 Technical Details

### Built With:
- **Python 3.9+** with tkinter GUI
- **yt-dlp** for video downloading
- **FFmpeg** for live stream merging
- **Threading** for non-blocking downloads
- **Subprocess** for yt-dlp execution

### Command Research:
This application uses commands researched from:
- Official yt-dlp documentation
- Community best practices
- Reddit discussions and forums
- Performance testing and optimization

### Documentation:
- **[Command Reference](docs/COMMANDS.md)** - Detailed yt-dlp command explanations
- **[FFmpeg Live Stream Guide](docs/FFMPEG_MERGING.md)** - Live stream merging documentation

## 🎉 Why EasyYTDLP?

- **Research-Based**: Commands based on extensive research
- **Optimal Quality**: True 1080p downloads with proper audio
- **Reliable**: Built-in error handling and retries
- **Fast**: Concurrent downloads and smart optimizations
- **Simple**: Clean GUI without overwhelming options
- **Cross-Platform**: Works on Windows, Mac, and Linux

## 📄 License

This project is released under the MIT License. See LICENSE file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit issues or pull requests.

---

**Created**: 2025-06-12
**Author**: Min
**Version**: 1.1.0 - FFmpeg Live Stream Merging
