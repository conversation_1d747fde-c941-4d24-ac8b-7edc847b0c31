# EasyYTDLP - Optimal Commands Reference

This document explains the optimal yt-dlp commands used in EasyYTDLP and provides additional command examples for advanced users.

## 🎯 Core Commands Used in EasyYTDLP

### 1080p Video Download (Default)
```bash
yt-dlp -f "bestvideo[height<=1080]+bestaudio/best[height<=1080]" \
       --merge-output-format mkv \
       --embed-metadata \
       --no-playlist \
       --restrict-filenames \
       --retries 10 \
       --fragment-retries 10 \
       --concurrent-fragments 4 \
       -o "filename.mkv" \
       "URL"
```

### Audio-Only Download (MP3)
```bash
yt-dlp -f "bestaudio/best" \
       -x --audio-format mp3 \
       --audio-quality 0 \
       --embed-metadata \
       --no-playlist \
       --restrict-filenames \
       --retries 10 \
       --fragment-retries 10 \
       -o "filename.%(ext)s" \
       "URL"
```

## 🔧 Command Breakdown

### Format Selection Logic
- **`bestvideo[height<=1080]`** - Best video quality up to 1080p
- **`+bestaudio`** - Combined with best audio quality
- **`/best[height<=1080]`** - Fallback to single file if merging fails

### Quality Filters
- **`[height<=1080]`** - Limit to 1080p maximum
- **`[height<=720]`** - Limit to 720p maximum
- **`[vcodec^=avc1]`** - Prefer H.264 codec
- **`[acodec^=mp4a]`** - Prefer AAC audio codec

### Output Options
- **`--merge-output-format mkv`** - Force MKV container
- **`--embed-metadata`** - Add title, description, etc.
- **`--restrict-filenames`** - Safe filenames for all OS
- **`--no-playlist`** - Download single video only

### Performance Options
- **`--concurrent-fragments 4`** - Download 4 fragments simultaneously
- **`--retries 10`** - Retry failed downloads 10 times
- **`--fragment-retries 10`** - Retry failed fragments 10 times

## 📋 Alternative Commands for Advanced Users

### Maximum Quality (Any Resolution)
```bash
yt-dlp -f "bestvideo+bestaudio/best" --merge-output-format mkv "URL"
```

### Specific Codec Preference
```bash
yt-dlp -f "bestvideo[height<=1080][vcodec^=avc1]+bestaudio[acodec^=mp4a]/bestvideo[height<=1080]+bestaudio" --merge-output-format mkv "URL"
```

### Size-Limited Download
```bash
yt-dlp -f "bestvideo[height<=1080][filesize<500M]+bestaudio/best[height<=1080]" --merge-output-format mkv "URL"
```

### Progressive Format Preference (Faster)
```bash
yt-dlp -f "best[height<=1080]/bestvideo[height<=1080]+bestaudio" --merge-output-format mkv "URL"
```

### Avoid Specific Codecs
```bash
yt-dlp -f "bestvideo[height<=1080][vcodec!*=vp9]+bestaudio/bestvideo[height<=1080]+bestaudio" --merge-output-format mkv "URL"
```

## 🎵 Audio-Only Variations

### High-Quality FLAC
```bash
yt-dlp -f "bestaudio" -x --audio-format flac "URL"
```

### Specific Audio Quality
```bash
yt-dlp -f "bestaudio" -x --audio-format mp3 --audio-quality 192K "URL"
```

### Keep Original Audio Format
```bash
yt-dlp -f "bestaudio" --embed-metadata "URL"
```

## 📺 Video-Only Downloads

### Video Without Audio
```bash
yt-dlp -f "bestvideo[height<=1080]" --merge-output-format mkv "URL"
```

### Specific Video Codec
```bash
yt-dlp -f "bestvideo[height<=1080][vcodec^=avc1]" "URL"
```

## 🔍 Information Commands

### List Available Formats
```bash
yt-dlp -F "URL"
```

### Get Video Information
```bash
yt-dlp --print "%(title)s - %(duration_string)s - %(resolution)s" "URL"
```

### Check What Would Be Downloaded
```bash
yt-dlp -f "bestvideo[height<=1080]+bestaudio" --print filename "URL"
```

## 📝 Subtitle Options

### Download with Subtitles
```bash
yt-dlp -f "bestvideo[height<=1080]+bestaudio" --write-subs --sub-langs en --embed-subs --merge-output-format mkv "URL"
```

### Auto-Generated Subtitles
```bash
yt-dlp -f "bestvideo[height<=1080]+bestaudio" --write-auto-subs --sub-langs en --embed-subs --merge-output-format mkv "URL"
```

### All Available Subtitles
```bash
yt-dlp -f "bestvideo[height<=1080]+bestaudio" --write-subs --sub-langs all --embed-subs --merge-output-format mkv "URL"
```

## 🎬 Playlist Commands

### Download Entire Playlist
```bash
yt-dlp -f "bestvideo[height<=1080]+bestaudio" --merge-output-format mkv "PLAYLIST_URL"
```

### Download First N Videos
```bash
yt-dlp -f "bestvideo[height<=1080]+bestaudio" --merge-output-format mkv --playlist-end 10 "PLAYLIST_URL"
```

### Download Specific Range
```bash
yt-dlp -f "bestvideo[height<=1080]+bestaudio" --merge-output-format mkv --playlist-start 5 --playlist-end 15 "PLAYLIST_URL"
```

## 🚀 Performance Optimization

### Maximum Speed
```bash
yt-dlp -f "bestvideo[height<=1080]+bestaudio" \
       --merge-output-format mkv \
       --concurrent-fragments 8 \
       --retries 5 \
       --fragment-retries 5 \
       --socket-timeout 30 \
       "URL"
```

### Conservative (Reliable)
```bash
yt-dlp -f "bestvideo[height<=1080]+bestaudio" \
       --merge-output-format mkv \
       --retries 20 \
       --fragment-retries 20 \
       --sleep-interval 1 \
       --max-sleep-interval 5 \
       "URL"
```

## 🎯 Why These Commands Are Optimal

1. **Format Selection**: `bestvideo[height<=1080]+bestaudio` ensures true 1080p quality with best audio
2. **MKV Container**: Universal compatibility and reliable merging
3. **Fallback Options**: `/best[height<=1080]` provides backup if merging fails
4. **Performance**: Concurrent fragments and retry logic for reliability
5. **Metadata**: Embedded information for better file organization
6. **Cross-Platform**: Restricted filenames work on all operating systems

## 📊 Quality Comparison

| Command | Video Quality | Audio Quality | Speed | Compatibility |
|---------|---------------|---------------|-------|---------------|
| `best` | Limited to 720p | Good | Fast | High |
| `bestvideo+bestaudio` | Maximum | Maximum | Medium | Medium |
| `bestvideo[height<=1080]+bestaudio` | True 1080p | Maximum | Medium | High |
| Our Optimal | True 1080p | Maximum | Fast | Maximum |

---

**Note**: These commands are used internally by EasyYTDLP. For manual use, ensure you have yt-dlp and ffmpeg installed.
