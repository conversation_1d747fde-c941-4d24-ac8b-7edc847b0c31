# FFmpeg Live Stream Merging Feature

## Overview

EasyYTDLP now includes automatic FFmpeg merging for partial live stream downloads. When you stop a live stream download mid-way, the application will automatically attempt to merge any partial video and audio files into a single playable MKV file.

## How It Works

### Live Stream Detection
- The application automatically detects YouTube URLs that could be live streams
- Live streams use DASH format with separate video and audio streams
- When stopped mid-download, these create partial `.part` files

### Automatic Merging Process
1. **Stop Download**: When you use "Stop Download" on a live stream
2. **File Detection**: The app searches for partial files:
   - `*.part` files (video and audio segments)
   - `*.ytdl` temporary files
   - `*.frag*` fragment files
3. **FFmpeg Merge**: Automatically runs FFmpeg to merge files:
   ```bash
   ffmpeg -i video.part -i audio.part -c copy output_merged.mkv
   ```
4. **Result**: Single playable MKV file with smooth seeking

### FFmpeg Commands Used

#### For Separate Video/Audio Files:
```bash
ffmpeg -y -i video.part -i audio.part -c copy -map 0:v:0 -map 1:a:0 output_merged.mkv
```

#### For Fragment Files:
```bash
ffmpeg -y -f concat -safe 0 -i filelist.txt -c copy output.mkv
```

#### For Single Partial File:
```bash
ffmpeg -y -i partial.part -c copy output.mkv
```

## Requirements

### FFmpeg Installation
- **Windows**: Download from https://ffmpeg.org/download.html
- **Add to PATH**: Ensure `ffmpeg.exe` is accessible from command line
- **Test**: Run `ffmpeg -version` in command prompt

### Supported Scenarios
- ✅ YouTube live streams (ongoing or ended)
- ✅ DASH format downloads with separate video/audio
- ✅ Partial downloads stopped mid-stream
- ✅ Fragment-based downloads

## Usage Instructions

### For Live Streams:
1. **Paste YouTube live stream URL**
2. **Start Download**: Begin downloading normally
3. **Stop When Needed**: Click "Stop Download" when you have enough content
4. **Automatic Merge**: App will automatically merge partial files
5. **Result**: Get `filename_merged.mkv` ready for playback

### Manual FFmpeg Commands
If automatic merging fails, you can manually merge files:

```bash
# Find partial files in download directory
dir *.part

# Merge video and audio
ffmpeg -i video.f136.part -i audio.f140.part -c copy merged_output.mkv

# Or merge fragments
ffmpeg -f concat -safe 0 -i filelist.txt -c copy output.mkv
```

## Benefits

### For Live Stream Highlights:
- **Fast Processing**: No re-encoding, just container merging
- **Quality Preservation**: Original quality maintained
- **Smooth Seeking**: Properly merged files support timeline scrubbing
- **Social Media Ready**: Perfect for extracting TikTok highlights

### Technical Advantages:
- **Stream Copy**: Uses `-c copy` for fastest processing
- **No Quality Loss**: No re-encoding means no quality degradation
- **Efficient**: Merging is much faster than downloading entire streams
- **Reliable**: FFmpeg is industry-standard for media processing

## Troubleshooting

### FFmpeg Not Found:
```
❌ FFmpeg not found - cannot merge partial files
💡 Install FFmpeg to enable automatic merging of partial downloads
```
**Solution**: Install FFmpeg and add to system PATH

### No Partial Files Found:
```
⚠️ No partial files found to merge
```
**Possible Causes**:
- Download completed successfully (no partial files)
- Files were cleaned up by yt-dlp
- Download failed before creating partial files

### Merge Failed:
```
❌ FFmpeg merge failed: [error message]
```
**Solutions**:
- Check FFmpeg installation
- Verify partial files exist and are not corrupted
- Try manual FFmpeg command with verbose output

## File Naming Convention

### Input Files (Partial):
- `filename.f136.part` (video stream)
- `filename.f140.part` (audio stream)
- `filename.ytdl` (metadata)

### Output Files (Merged):
- `filename_merged.mkv` (final merged file)

## Performance Notes

### Speed Expectations:
- **Merging Time**: Usually 10-30 seconds for hours of content
- **CPU Usage**: Minimal (stream copy, no encoding)
- **Disk Space**: Temporary increase during merge process

### Optimization Tips:
- Ensure sufficient disk space (2x content size during merge)
- Use SSD for faster file operations
- Close other applications during merge for best performance

## Integration with EasyYTDLP

### GUI Indicators:
- 🔧 "Searching for partial files to merge..."
- ✅ "Successfully merged partial files to: filename_merged.mkv"
- ⚠️ "Could not merge partial files - check if FFmpeg is installed"

### Log Messages:
- Live stream detection
- Partial file discovery
- FFmpeg execution status
- Merge success/failure notifications

This feature makes EasyYTDLP perfect for downloading specific segments from long live streams without needing to download the entire stream!
