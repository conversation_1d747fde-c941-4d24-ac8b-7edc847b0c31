# EasyYTDLP Quick Start Guide

## 🚀 Getting Started

### 1. **Download & Setup**
- Ensure `yt-dlp.exe` is in the same folder as `main.py`
- Install FFmpeg for live stream merging (optional but recommended)
- Run: `python main.py` or double-click `run_easytdlp.bat`

### 2. **Basic Usage**
1. **Paste YouTube URL** in the URL field
2. **Enter filename** (or click Auto-fill)
3. **Select quality** (1080p recommended)
4. **Click "Start Download"**

## 📺 Download Types

### **Normal Videos (Most Common)**
- ✅ **Quality**: 1080p with best audio
- ✅ **Format**: MKV container
- ✅ **Speed**: Fast concurrent downloads
- ✅ **Output**: Single ready-to-play file

### **Live Streams**
- 🔧 **Auto-Detection**: Automatically detected
- 🔧 **Stop Mid-Stream**: Use "Stop Download" anytime
- 🔧 **Auto-Merge**: FFmpeg merges partial files automatically
- 🔧 **Output**: `filename_merged.mkv`

### **Audio-Only**
- 🎵 **Check "Audio Only"** checkbox
- 🎵 **Format**: High-quality MP3
- 🎵 **Quality**: Best available audio

## 🎯 Quality Settings

| Setting | Description | Best For |
|---------|-------------|----------|
| **1080p** | True 1080p + best audio | **Recommended** |
| **720p** | HD quality, smaller files | Slower connections |
| **480p** | Standard definition | Very slow connections |
| **Best Available** | Highest quality possible | Maximum quality |

## 🔧 Live Stream Features

### **For Content Creators:**
- **Download live streams** while they're happening
- **Stop at any point** to get partial content
- **Automatic merging** creates playable files
- **Perfect for TikTok** highlight extraction

### **How It Works:**
1. Start downloading a live stream
2. Stop when you have enough content
3. FFmpeg automatically merges partial files
4. Get smooth, seekable video ready for editing

## ⚡ Performance Tips

### **For Best Speed:**
- Use **1080p setting** (optimal balance)
- Ensure **good internet connection**
- Close **bandwidth-heavy applications**
- Use **wired connection** for large downloads

### **For Live Streams:**
- Install **FFmpeg** for automatic merging
- Use **"Stop Download"** instead of closing app
- Allow **few seconds** for merge processing

## 🛠 Troubleshooting

### **Common Issues:**

**"yt-dlp.exe not found"**
- Download from: https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp.exe
- Place in same folder as main.py

**"FFmpeg not found" (for live streams)**
- Download FFmpeg from: https://ffmpeg.org/download.html
- Add to system PATH or place in app folder

**"Download failed"**
- Check internet connection
- Try different quality setting
- Verify video is available in your region

**Slow downloads**
- Lower quality setting
- Check internet speed
- Close other applications

## 📁 Output Files

### **Normal Downloads:**
- `filename.mkv` - Ready to play

### **Live Stream (Stopped Mid-Download):**
- `filename_merged.mkv` - FFmpeg merged file
- `filename.part` files - Temporary (can be deleted)

### **Audio-Only:**
- `filename.mp3` - High-quality audio

## 🎬 Perfect for Content Creation

### **TikTok Workflow:**
1. **Find long live stream** or competition video
2. **Start download** with EasyYTDLP
3. **Stop when you have enough** content
4. **Get merged MKV file** ready for editing
5. **Extract highlights** for social media

### **Quality Benefits:**
- **No re-encoding** = No quality loss
- **Fast merging** = Seconds, not hours
- **Smooth seeking** = Perfect for editing
- **1080p quality** = Professional results

## 📋 File Management

### **Recommended Workflow:**
1. Create **dedicated download folder**
2. Use **descriptive filenames**
3. **Clean up .part files** after successful merges
4. **Backup important downloads**

### **Storage Tips:**
- **1080p videos**: ~100-500MB per 10 minutes
- **Live streams**: Varies greatly by content
- **Audio-only**: ~10-50MB per 10 minutes

---

**Need more help?** Check the full documentation:
- [README.md](../README.md) - Complete feature overview
- [COMMANDS.md](COMMANDS.md) - Technical command details
- [FFMPEG_MERGING.md](FFMPEG_MERGING.md) - Live stream merging guide
