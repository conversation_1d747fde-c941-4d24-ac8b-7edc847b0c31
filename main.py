#!/usr/bin/env python3
"""
EasyYTDLP - Simplified YouTube Downloader with Automatic Audio+Video Merging
A clean, efficient YouTube downloader using researched yt-dlp best practices

Features:
- Automatic audio+video merging with default yt-dlp behavior
- Optimal yt-dlp format selection prioritizing single files
- True 1080p quality with smart fallback handling
- Live stream support with proper fragment handling
- Clean MKV output with automatic cleanup
- No separate audio/video files left behind

Version: 2.1.0
Created: 2025-06-12
Updated: 2025-06-20 - Fixed automatic audio+video merging
Author: Min
"""

import os
import sys
import time
import threading
import subprocess
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import re
from datetime import datetime

class DownloadManager:
    """Simplified download manager with optimal yt-dlp commands"""

    def __init__(self, gui_callback):
        self.gui_callback = gui_callback
        self.process = None
        self.is_downloading = False
        self.download_cancelled = False
        self.graceful_stop = False
        self.current_output_dir = None
        self.current_filename = None

    def start_download(self, url, filename, output_dir, quality="1080p", audio_only=False, stream_start="from_now", simple_mode=False):
        """Start download with optimal yt-dlp commands"""
        if self.is_downloading:
            return False

        self.is_downloading = True
        self.download_cancelled = False
        self.graceful_stop = False
        self.current_output_dir = output_dir
        self.current_filename = filename

        # Build optimal command based on research findings
        cmd = self._build_optimal_command(url, filename, output_dir, quality, audio_only, stream_start, simple_mode)

        # Start download in separate thread
        thread = threading.Thread(target=self._run_download, args=(cmd,), daemon=True)
        thread.start()

        return True

    def stop_download(self):
        """Stop the current download immediately"""
        if not self.is_downloading or not self.process:
            return False

        self.download_cancelled = True
        try:
            self.process.terminate()
            self.gui_callback("log", "🛑 Download stopped by user")
        except:
            pass

        return True

    def finish_here(self):
        """Gracefully stop download and keep partial file"""
        if not self.is_downloading or not self.process:
            return False

        self.graceful_stop = True
        try:
            self.gui_callback("log", "⏹️ Finishing download at current point...")

            # Stop the download process
            if self.process.poll() is None:
                self.process.terminate()
                try:
                    self.process.wait(timeout=3)
                    self.gui_callback("log", "⏹️ Download stopped successfully")
                except:
                    if self.process.poll() is None:
                        self.process.kill()
                        self.gui_callback("log", "⏹️ Download force stopped")

            self.gui_callback("log", "✅ Partial file should be ready for playback")

            # Wait a moment for yt-dlp to fully release file handles
            import time
            time.sleep(2.0)

            # Try to fix the partial file for better playback
            self._fix_partial_download()

        except Exception as e:
            self.gui_callback("log", f"⚠️ Warning during finish here: {str(e)}")
            if self.process and self.process.poll() is None:
                self.process.kill()
        finally:
            # Ensure all FFmpeg processes are cleaned up after finish here
            self.gui_callback("log", "🔧 Cleaning up any remaining processes...")
            self._kill_all_ffmpeg_processes()

        return True

    def _build_optimal_command(self, url, filename, output_dir, quality, audio_only, stream_start, simple_mode=False):
        """Build optimal yt-dlp command with researched best practices"""
        cmd = ["yt-dlp"]

        # Base command with optimal settings for automatic merging
        cmd.extend([
            url,
            "--newline",
            "--no-playlist",
            "--restrict-filenames",
            "--embed-metadata",
            "--retries", "10",
            "--fragment-retries", "10",
            "--socket-timeout", "30",
            "--extractor-retries", "3",
            "--no-part",  # Disable .part files
            "--force-overwrites",  # Overwrite existing files
            "--prefer-ffmpeg"      # Use FFmpeg for merging (yt-dlp deletes intermediate files by default)
        ])

        # Output template and format selection
        if audio_only:
            output_template = os.path.join(output_dir, f"{filename}.%(ext)s")
            cmd.extend([
                "-o", output_template,
                "-f", "bestaudio/best",
                "-x", "--audio-format", "mp3",
                "--audio-quality", "0"
            ])
        else:
            # Force MKV output for video
            output_template = os.path.join(output_dir, f"{filename}.mkv")
            cmd.extend(["-o", output_template])

            # Optimal format selection - prioritize single files, then auto-merge
            if quality == "1080p":
                # STRICT 1080p - prioritize single files to avoid separate downloads
                format_string = (
                    "best[height=1080][ext=mp4][acodec!=none]/"                 # Single 1080p MP4 with audio (preferred)
                    "best[height=1080][acodec!=none]/"                          # Single 1080p any format with audio
                    "bestvideo[height=1080][vcodec^=avc1]+bestaudio[ext=m4a]/"  # Exact 1080p H.264 + M4A (auto-merge)
                    "bestvideo[height=1080]+bestaudio/"                         # Exact 1080p + audio (auto-merge)
                    "best[height=1080]"                                         # Single file 1080p fallback
                )
            elif quality == "720p":
                format_string = (
                    "best[height=720][ext=mp4][acodec!=none]/"                  # Single 720p MP4 with audio (preferred)
                    "best[height=720][acodec!=none]/"                           # Single 720p any format with audio
                    "bestvideo[height=720][vcodec^=avc1]+bestaudio[ext=m4a]/"   # 720p H.264 + M4A (auto-merge)
                    "bestvideo[height=720]+bestaudio/"                          # 720p + audio (auto-merge)
                    "best[height=720]"                                          # Single file 720p fallback
                )
            elif quality == "480p":
                format_string = (
                    "best[height=480][ext=mp4][acodec!=none]/"                  # Single 480p MP4 with audio (preferred)
                    "best[height=480][acodec!=none]/"                           # Single 480p any format with audio
                    "bestvideo[height=480][vcodec^=avc1]+bestaudio[ext=m4a]/"   # 480p H.264 + M4A (auto-merge)
                    "bestvideo[height=480]+bestaudio/"                          # 480p + audio (auto-merge)
                    "best[height=480]"                                          # Single file 480p fallback
                )
            else:  # Best available
                format_string = (
                    "best[ext=mp4][acodec!=none]/"                              # Single MP4 with audio (preferred)
                    "best[acodec!=none]/"                                       # Single file with audio
                    "bestvideo[vcodec^=avc1]+bestaudio[ext=m4a]/"               # H.264 + M4A (auto-merge)
                    "bestvideo+bestaudio/"                                      # Best video + audio (auto-merge)
                    "best"                                                      # Final fallback
                )

            cmd.extend([
                "-f", format_string,
                "--merge-output-format", "mkv",                      # Force MKV output
                "--postprocessor-args", "ffmpeg:-c:v copy -c:a aac", # Fast copy + AAC audio
                "--prefer-ffmpeg"                                    # Use FFmpeg for merging (auto-detect location)
            ])

        # Live stream specific options
        is_youtube = 'youtube.com' in url.lower() or 'youtu.be' in url.lower()
        if is_youtube:            if stream_start == "from_start":
                cmd.extend([
                    "--live-from-start",
                    "--keep-fragments",
                    "--hls-use-mpegts",
                    "--ignore-errors",
                    "--no-abort-on-error",
                    "--concurrent-fragments", "1",  # Reduce concurrency for stability
                    "--force-overwrites",          # Force overwrite for better merging
                    "--no-keep-video",            # Don't keep separate video file
                    "--no-keep-fragments",        # Don't keep fragments after merge
                    "--remux-video", "mkv"        # Force remux to MKV
                ])
                self.gui_callback("log", "📺 Live stream mode: From Start")
            else:
                cmd.extend([
                    "--keep-fragments",
                    "--hls-use-mpegts",
                    "--ignore-errors",
                    "--no-abort-on-error",
                    "--concurrent-fragments", "1"  # Reduce concurrency for stability
                ])
                self.gui_callback("log", "📺 Live stream mode: From Now")            # For live streams, use a more compatible format selection
            if not audio_only:
                if stream_start == "from_start":
                    # Special format selection for "from start" mode to ensure proper merging
                    self.gui_callback("log", "🔧 Using optimized format selection for live stream from start")
                    if quality == "1080p":
                        format_string = (
                            "bestvideo[height<=1080][vcodec^=avc1]+bestaudio[acodec^=mp4a]/"+  # H.264 video + AAC audio
                            "bestvideo[height<=1080]+bestaudio/"+                               # Any 1080p + best audio
                            "best[height<=1080]"                                                # Single file fallback
                        )
                    elif quality == "720p":
                        format_string = (
                            "bestvideo[height<=720][vcodec^=avc1]+bestaudio[acodec^=mp4a]/"+   # H.264 video + AAC audio
                            "bestvideo[height<=720]+bestaudio/"+                                # Any 720p + best audio
                            "best[height<=720]"                                                 # Single file fallback
                        )
                    else:
                        format_string = (
                            "bestvideo[vcodec^=avc1]+bestaudio[acodec^=mp4a]/"+                # H.264 video + AAC audio
                            "bestvideo+bestaudio/"+                                            # Any video + best audio
                            "best"                                                             # Single file fallback
                        )
                    # Update format selection
                    cmd[cmd.index("-f") + 1] = format_string
                    
                elif simple_mode:
                    # Simple mode: prioritize single files to avoid separate downloads
                    self.gui_callback("log", "🔧 Simple mode enabled - prioritizing single files with audio")
                    if quality == "1080p":
                        format_string = (
                            "best[height=1080][ext=mp4][acodec!=none]/"    # Single 1080p MP4 with audio (preferred)
                            "best[height=1080][acodec!=none]/"             # Single 1080p any format with audio
                            "bestvideo[height=1080]+bestaudio/"            # 1080p video + audio (auto-merge)
                            "best[height<=1080][height>=1080][acodec!=none]" # Force 1080p range with audio
                        )
                    elif quality == "720p":
                        format_string = (
                            "best[height=720][ext=mp4][acodec!=none]/"     # Single 720p MP4 with audio (preferred)
                            "best[height=720][acodec!=none]/"              # Single 720p with audio
                            "bestvideo[height=720]+bestaudio/"             # 720p video + audio (auto-merge)
                            "best[height=720]"                             # 720p fallback
                        )
                    elif quality == "480p":
                        format_string = (
                            "best[height=480][ext=mp4][acodec!=none]/"     # Single 480p MP4 with audio (preferred)
                            "best[height=480][acodec!=none]/"              # Single 480p with audio
                            "bestvideo[height=480]+bestaudio/"             # 480p video + audio (auto-merge)
                            "best[height=480]"                             # 480p fallback
                        )
                    else:
                        format_string = (
                            "best[ext=mp4][acodec!=none]/"                 # Single MP4 with audio (preferred)
                            "best[acodec!=none]/"                          # Single file with audio
                            "bestvideo+bestaudio/"                         # Best video + audio (auto-merge)
                            "best"                                         # Final fallback
                        )
                else:
                    # Normal mode: prioritize single files, then auto-merge
                    if quality == "1080p":
                        format_string = (
                            "best[height=1080][ext=mp4][acodec!=none]/"            # Single 1080p MP4 with audio (preferred)
                            "best[height=1080][acodec!=none]/"                     # Single 1080p with audio
                            "bestvideo[height=1080][vcodec^=avc1]+bestaudio/"      # 1080p H.264 + audio (auto-merge)
                            "bestvideo[height=1080]+bestaudio/"                    # 1080p + audio (auto-merge)
                            "best[height=1080]"                                    # 1080p fallback
                        )
                    elif quality == "720p":
                        format_string = (
                            "best[height=720][ext=mp4][acodec!=none]/"             # Single 720p MP4 with audio (preferred)
                            "best[height=720][acodec!=none]/"                      # Single 720p with audio
                            "bestvideo[height=720]+bestaudio/"                     # 720p video + audio (auto-merge)
                            "best[height=720]"                                     # 720p fallback
                        )
                    elif quality == "480p":
                        format_string = (
                            "best[height=480][ext=mp4][acodec!=none]/"             # Single 480p MP4 with audio (preferred)
                            "best[height=480][acodec!=none]/"                      # Single 480p with audio
                            "bestvideo[height=480]+bestaudio/"                     # 480p video + audio (auto-merge)
                            "best[height=480]"                                     # 480p fallback
                        )
                    else:  # Best available
                        format_string = (
                            "best[ext=mp4][acodec!=none]/"                         # Single MP4 with audio (preferred)
                            "best[acodec!=none]/"                                  # Single file with audio
                            "bestvideo+bestaudio/"                                 # Best video + audio (auto-merge)
                            "best"                                                 # Final fallback
                        )

                # Update the format string for live streams
                cmd[cmd.index("-f") + 1] = format_string

        return cmd

    def _run_download(self, cmd):
        """Execute the download command"""
        try:
            # Hide console window on Windows
            startupinfo = None
            if os.name == 'nt':
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE

            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                stdin=subprocess.PIPE,
                encoding='utf-8',
                errors='replace',
                startupinfo=startupinfo
            )

            # Monitor output for progress
            progress_pattern = re.compile(r"\[download\]\s+(\d+\.\d+)%")

            if self.process.stdout:
                for line in iter(self.process.stdout.readline, ''):
                    if self.download_cancelled or self.graceful_stop:
                        break

                    self.gui_callback("log", line.strip())

                    # Extract progress percentage
                    match = progress_pattern.search(line)
                    if match:
                        percent = float(match.group(1))
                        self.gui_callback("progress", percent)

                    # Check for quality and audio information
                    if "1920x1080" in line:
                        self.gui_callback("log", "✅ Confirmed: TRUE 1080p quality (1920x1080)")
                    elif "1280x720" in line:
                        self.gui_callback("log", "⚠️ Warning: Only 720p available (1280x720)")
                    elif "Stream #" in line and "Video:" in line:
                        self.gui_callback("log", f"📺 Video stream detected: {line.strip()}")
                    elif "Stream #" in line and "Audio:" in line:
                        self.gui_callback("log", f"🔊 Audio stream detected: {line.strip()}")
                    elif "[download] Destination:" in line and ".mkv" in line:
                        self.gui_callback("log", f"📁 Output file: {line.split('Destination: ')[-1].strip()}")
                    elif "Merging formats into" in line:
                        self.gui_callback("log", f"🔗 yt-dlp is automatically merging video and audio...")
                    elif "Deleting original file" in line:
                        self.gui_callback("log", f"🗑️ yt-dlp automatically deleted separate file")
                    elif "has already been downloaded" in line:
                        self.gui_callback("log", f"✅ File already exists and is complete")
                    elif "[Merger]" in line:
                        self.gui_callback("log", f"🔗 Auto-merge in progress: {line.strip()}")
                    elif "ffmpeg" in line.lower() and ("merging" in line.lower() or "combining" in line.lower()):
                        self.gui_callback("log", f"🔧 FFmpeg auto-merge: {line.strip()}")

            # Check final result
            if not self.download_cancelled and not self.graceful_stop and self.process:
                result = self.process.wait()
                if result == 0:
                    self.gui_callback("log", "✅ Download completed successfully!")
                    self.gui_callback("log", "🔍 Checking for automatic merge completion...")
                    # Wait for yt-dlp to fully release file handles
                    import time
                    time.sleep(2.0)
                    # Check for .part files and handle any remaining separate files
                    self._handle_part_files()
                    self.gui_callback("download_finished", "success")
                else:
                    self.gui_callback("log", f"❌ Download failed with exit code {result}")

                    # Check if it's a live stream merge error or quality issue
                    if "ffmpeg exited with code" in str(result) or result == 1:
                        self.gui_callback("log", "💡 Possible causes:")
                        self.gui_callback("log", "   • Live stream merge error - try Simple Mode")
                        self.gui_callback("log", "   • 1080p not available - stream may only have 720p")
                        self.gui_callback("log", "   • Try 'From Start' mode or check if stream is still live")
                        self.gui_callback("log", "   • Enable Simple Mode for problematic streams")

                    # Still try to handle .part files even if download "failed"
                    self._handle_part_files()
                    self.gui_callback("download_finished", "failed")
            elif self.graceful_stop:
                self.gui_callback("log", "⏹️ Download finished at current point")
                # Handle .part files for graceful stops
                self._handle_part_files()
                self.gui_callback("download_finished", "finished_here")
            else:
                self.gui_callback("download_finished", "cancelled")

        except Exception as e:
            self.gui_callback("log", f"❌ Download error: {e}")
            self.gui_callback("download_finished", "error")

        finally:
            self.is_downloading = False
            self.process = None

    def _handle_part_files(self):
        """Handle .part files and check for any remaining separate video/audio files"""
        try:
            # Use the download directory
            search_dir = self.current_output_dir if self.current_output_dir else os.getcwd()

            # First, check if yt-dlp left any separate files that need merging
            # (This should be rare since yt-dlp deletes intermediate files by default)
            self._auto_merge_video_audio()

            # Then handle any remaining .part files
            part_files = []
            for ext in ['.part', '.mkv.part', '.mp4.part', '.webm.part']:
                part_files.extend([f for f in os.listdir(search_dir) if f.endswith(ext)])

            if not part_files:
                self.gui_callback("log", "✅ No .part files found - yt-dlp completed merge automatically")
                return

            self.gui_callback("log", f"🔧 Found {len(part_files)} .part file(s) - processing...")

            for part_file in part_files:
                part_path = os.path.join(search_dir, part_file)

                # Determine the final filename
                if part_file.endswith('.mkv.part'):
                    final_name = part_file.replace('.mkv.part', '.mkv')
                elif part_file.endswith('.mp4.part'):
                    final_name = part_file.replace('.mp4.part', '.mkv')
                elif part_file.endswith('.webm.part'):
                    final_name = part_file.replace('.webm.part', '.mkv')
                else:
                    final_name = part_file.replace('.part', '.mkv')

                final_path = os.path.join(search_dir, final_name)

                # Check if file has content
                if os.path.getsize(part_path) > 1024:  # At least 1KB
                    try:
                        # Try to fix the file with FFmpeg for proper playback
                        # The _fix_corrupted_video method will automatically delete the input file if successful
                        if self._fix_corrupted_video(part_path, final_path):
                            self.gui_callback("log", f"✅ Fixed & Converted: {part_file} → {final_name}")
                        else:
                            # FFmpeg fix failed, try simple rename
                            try:
                                os.rename(part_path, final_path)
                                self.gui_callback("log", f"⚠️ Converted (may need manual fix): {part_file} → {final_name}")
                            except:
                                # If rename fails, try copy
                                import shutil
                                shutil.copy2(part_path, final_path)
                                try:
                                    os.remove(part_path)
                                except:
                                    self.gui_callback("log", f"⚠️ Could not delete original {part_file}")
                                self.gui_callback("log", f"⚠️ Copied (may need manual fix): {part_file} → {final_name}")

                    except Exception as e:
                        self.gui_callback("log", f"⚠️ Could not convert {part_file}: {e}")
                else:
                    self.gui_callback("log", f"⚠️ Skipping empty file: {part_file}")

        except Exception as e:
            self.gui_callback("log", f"⚠️ Error handling .part files: {e}")

    def _auto_merge_video_audio(self):
        """Check for any remaining separate video and audio files (should be rare with new settings)"""
        try:
            if not self.current_output_dir or not self.current_filename:
                return

            search_dir = self.current_output_dir
            base_filename = self.current_filename

            # Look for video and audio files with the same base name
            video_files = []
            audio_files = []

            # Common patterns for separate video/audio files
            video_extensions = ['.mp4', '.mkv', '.webm', '.avi']
            audio_extensions = ['.m4a', '.mp3', '.aac', '.opus', '.ogg']

            for file in os.listdir(search_dir):
                file_path = os.path.join(search_dir, file)
                if not os.path.isfile(file_path):
                    continue

                # Check if file matches our download pattern
                if base_filename in file:
                    file_lower = file.lower()

                    # Check for video files
                    for ext in video_extensions:
                        if file_lower.endswith(ext):
                            video_files.append(file_path)
                            break

                    # Check for audio files
                    for ext in audio_extensions:
                        if file_lower.endswith(ext):
                            audio_files.append(file_path)
                            break

            # If we found both video and audio files, yt-dlp didn't auto-merge them
            if video_files and audio_files:
                self.gui_callback("log", f"⚠️ Found separate video and audio files (yt-dlp didn't auto-merge):")
                for vf in video_files:
                    self.gui_callback("log", f"  📹 Video: {os.path.basename(vf)}")
                for af in audio_files:
                    self.gui_callback("log", f"  🔊 Audio: {os.path.basename(af)}")

                # Try to merge the first video with the first audio
                video_file = video_files[0]
                audio_file = audio_files[0]

                # Create merged filename
                merged_filename = f"{base_filename}_merged.mkv"
                merged_path = os.path.join(search_dir, merged_filename)

                self.gui_callback("log", "🎬 Performing manual video+audio merge...")

                if self.merge_audio_video(video_file, audio_file, merged_path):
                    self.gui_callback("log", "✅ Manual merge successful!")

                    # Clean up the original separate files
                    self.gui_callback("log", "🗑️ Cleaning up original separate files...")
                    try:
                        os.remove(video_file)
                        os.remove(audio_file)
                        self.gui_callback("log", "✅ Original separate files deleted")
                    except Exception as e:
                        self.gui_callback("log", f"⚠️ Could not delete original files: {e}")
                else:
                    self.gui_callback("log", "❌ Manual merge failed - keeping separate files")
            else:
                # This is the expected behavior with the new settings
                if video_files or audio_files:
                    self.gui_callback("log", "✅ Found single merged file - yt-dlp auto-merge worked correctly")

        except Exception as e:
            self.gui_callback("log", f"⚠️ Error during merge check: {e}")

    def _fix_corrupted_video(self, input_path, output_path):
        """Fix corrupted video files using FFmpeg to create proper seekable files"""
        ffmpeg_process = None
        try:
            self.gui_callback("log", "🔧 Attempting to fix video with FFmpeg...")

            # Kill any existing FFmpeg processes first to prevent conflicts
            self._kill_all_ffmpeg_processes()
            import time
            time.sleep(1.0)  # Brief wait after cleanup

            # FFmpeg command to fix corrupted/incomplete videos
            cmd = [
                "ffmpeg",
                "-i", input_path,
                "-c", "copy",           # Copy streams without re-encoding (fast)
                "-avoid_negative_ts", "make_zero",  # Fix timestamp issues
                "-fflags", "+genpts",   # Generate presentation timestamps
                "-movflags", "+faststart",  # Optimize for streaming
                "-y",                   # Overwrite output file
                output_path
            ]

            # Hide console window on Windows with enhanced process isolation
            startupinfo = None
            creation_flags = 0
            if os.name == 'nt':
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                # Enhanced process isolation for better cleanup
                creation_flags = subprocess.CREATE_NEW_PROCESS_GROUP | subprocess.CREATE_BREAKAWAY_FROM_JOB

            # Run FFmpeg with enhanced process management
            ffmpeg_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                stdin=subprocess.PIPE,
                startupinfo=startupinfo,
                creationflags=creation_flags
            )

            # Wait for completion with timeout and proper cleanup
            try:
                stdout, stderr = ffmpeg_process.communicate(timeout=120)  # 2 minute timeout
                return_code = ffmpeg_process.returncode
                self.gui_callback("log", f"🔧 FFmpeg completed with exit code: {return_code}")
            except subprocess.TimeoutExpired:
                self.gui_callback("log", "⚠️ FFmpeg fix timed out - force terminating process")
                self._force_kill_ffmpeg_process(ffmpeg_process)
                return False
            except Exception as e:
                self.gui_callback("log", f"⚠️ FFmpeg communication error: {e}")
                self._force_kill_ffmpeg_process(ffmpeg_process)
                return False

            # Ensure process is fully terminated
            self._force_kill_ffmpeg_process(ffmpeg_process)

            # Kill any remaining FFmpeg processes system-wide
            self._kill_all_ffmpeg_processes()

            # Verify processes are terminated
            self._verify_ffmpeg_processes_terminated()

            # Extended wait for Windows file handle release and process cleanup
            self.gui_callback("log", "⏳ Waiting for file handles to be released...")
            time.sleep(5.0)  # Longer wait for complete cleanup

            if return_code == 0 and os.path.exists(output_path):
                # Check if output file is larger than 1KB
                if os.path.getsize(output_path) > 1024:
                    self.gui_callback("log", "✅ FFmpeg fix successful - video should now be fully playable")

                    # Wait for file release before attempting replacement
                    self.gui_callback("log", "⏳ Waiting for file handles to be released...")
                    if self._wait_for_file_release(input_path, max_wait=30):
                        # Use safer file replacement strategy
                        success = self._safe_file_replace(input_path, output_path)
                        if success:
                            self.gui_callback("log", f"✅ Successfully replaced corrupted file with fixed version")
                        else:
                            self.gui_callback("log", f"⚠️ Fixed file created but could not replace original")
                            self.gui_callback("log", f"📁 Fixed file available at: {os.path.basename(output_path)}")
                    else:
                        self.gui_callback("log", f"⚠️ File still locked after cleanup - keeping both files")
                        self.gui_callback("log", f"📁 Fixed file available at: {os.path.basename(output_path)}")

                    return True
                else:
                    self.gui_callback("log", "❌ FFmpeg produced empty or invalid output")
                    self._cleanup_temp_file(output_path)
                    return False
            else:
                self.gui_callback("log", f"❌ FFmpeg fix failed with exit code {return_code}")
                self._cleanup_temp_file(output_path)
                return False

        except FileNotFoundError:
            self.gui_callback("log", "⚠️ FFmpeg not found - install FFmpeg for video fixing")
            return False
        except Exception as e:
            self.gui_callback("log", f"❌ Error during FFmpeg fix: {e}")
            if ffmpeg_process:
                self._force_kill_ffmpeg_process(ffmpeg_process)
            self._cleanup_temp_file(output_path)
            return False
        finally:
            # Final cleanup - ensure no FFmpeg processes remain
            self._kill_all_ffmpeg_processes()
            # Additional wait to ensure cleanup is complete
            import time
            time.sleep(1.0)

    def _safe_file_replace(self, original_path, fixed_path, max_attempts=15):
        """Safely replace original file with fixed file, handling Windows file locks"""
        import time
        import gc

        # First, ensure all FFmpeg processes are terminated
        self._kill_all_ffmpeg_processes()
        time.sleep(2.0)  # Wait for process cleanup

        for attempt in range(max_attempts):
            try:
                # Force garbage collection to release any Python file handles
                gc.collect()
                time.sleep(0.5)  # Brief pause after GC

                # Additional wait for file handles to be released
                if not self._wait_for_file_release(original_path, max_wait=10):
                    self.gui_callback("log", f"⚠️ File still locked after waiting, attempting anyway...")

                # First, try to rename original to backup
                backup_path = f"{original_path}.backup"

                # Remove any existing backup
                if os.path.exists(backup_path):
                    try:
                        os.remove(backup_path)
                    except:
                        pass

                # Try to rename original to backup
                try:
                    os.rename(original_path, backup_path)
                    self.gui_callback("log", f"📦 Created backup of original file")
                except Exception as e:
                    if attempt < max_attempts - 1:
                        self.gui_callback("log", f"⏳ Waiting for file to be released (attempt {attempt + 1}/{max_attempts})")
                        # Longer wait with exponential backoff
                        wait_time = min(3.0, 1.0 + (attempt * 0.5))
                        time.sleep(wait_time)
                        continue
                    else:
                        self.gui_callback("log", f"⚠️ Could not backup original file: {e}")
                        return False

                # Now try to rename fixed file to original name
                try:
                    os.rename(fixed_path, original_path)
                    self.gui_callback("log", f"✅ Fixed file moved to original location")

                    # Delete the backup
                    try:
                        os.remove(backup_path)
                        self.gui_callback("log", f"🗑️ Deleted backup file")
                    except:
                        self.gui_callback("log", f"⚠️ Could not delete backup file: {backup_path}")

                    return True

                except Exception as e:
                    # Restore backup if rename failed
                    try:
                        os.rename(backup_path, original_path)
                        self.gui_callback("log", f"🔄 Restored original file from backup")
                    except:
                        pass

                    if attempt < max_attempts - 1:
                        self.gui_callback("log", f"⏳ Retrying file replacement (attempt {attempt + 1}/{max_attempts})")
                        time.sleep(1.0)
                        continue
                    else:
                        self.gui_callback("log", f"⚠️ Could not replace file: {e}")
                        return False

            except Exception as e:
                if attempt < max_attempts - 1:
                    self.gui_callback("log", f"⏳ File operation failed, retrying... (attempt {attempt + 1}/{max_attempts})")
                    time.sleep(1.0)
                    continue
                else:
                    self.gui_callback("log", f"⚠️ File replacement failed: {e}")
                    return False

        return False

    def _fix_partial_download(self):
        """Fix partial downloads to ensure proper playback"""
        try:
            if not self.current_output_dir or not self.current_filename:
                return

            # Look for the downloaded file
            search_dir = self.current_output_dir

            # Find any video files that might need fixing
            video_files = []
            for ext in ['.mkv', '.mp4', '.webm']:
                pattern = os.path.join(search_dir, f"{self.current_filename}*{ext}")
                import glob
                video_files.extend(glob.glob(pattern))

            if not video_files:
                self.gui_callback("log", "🔍 No video files found to fix")
                return

            for video_file in video_files:
                if os.path.getsize(video_file) > 1024:  # At least 1KB
                    self.gui_callback("log", f"🔧 Fixing partial download: {os.path.basename(video_file)}")

                    # Create fixed version with same name (will replace original)
                    fixed_file = video_file.replace('.mkv', '_temp_fixed.mkv').replace('.mp4', '_temp_fixed.mkv').replace('.webm', '_temp_fixed.mkv')

                    # Note: _fix_corrupted_video now handles file replacement internally
                    if self._fix_corrupted_video(video_file, fixed_file):
                        self.gui_callback("log", f"✅ Successfully fixed: {os.path.basename(video_file)}")
                    else:
                        self.gui_callback("log", f"⚠️ Could not fix: {os.path.basename(video_file)}")
                        # Clean up failed temp file
                        if os.path.exists(fixed_file):
                            try:
                                os.remove(fixed_file)
                            except:
                                pass

        except Exception as e:
            self.gui_callback("log", f"⚠️ Error fixing partial download: {e}")

    def _force_kill_process(self, process):
        """Aggressively kill a process and ensure all handles are released"""
        try:
            if process.poll() is None:  # Process still running
                # Try graceful termination first
                process.terminate()
                try:
                    process.wait(timeout=2)
                except subprocess.TimeoutExpired:
                    # Force kill if graceful termination fails
                    process.kill()
                    process.wait()

            # On Windows, also try to kill by PID to ensure cleanup
            if os.name == 'nt' and hasattr(process, 'pid'):
                try:
                    import signal
                    os.kill(process.pid, signal.SIGTERM)
                except:
                    pass

        except Exception as e:
            self.gui_callback("log", f"⚠️ Error during process cleanup: {e}")

    def _force_kill_ffmpeg_process(self, process):
        """Enhanced FFmpeg-specific process termination with better cleanup"""
        import time

        try:
            if process and process.poll() is None:  # Process still running
                self.gui_callback("log", "🔧 Terminating FFmpeg process...")
                pid = process.pid if hasattr(process, 'pid') else None

                # Try graceful termination first
                process.terminate()
                try:
                    process.wait(timeout=2)  # Shorter timeout
                    self.gui_callback("log", "✅ FFmpeg process terminated gracefully")
                    return
                except subprocess.TimeoutExpired:
                    pass

                # Force kill if graceful termination fails
                self.gui_callback("log", "⚠️ Force killing FFmpeg process...")
                process.kill()
                try:
                    process.wait(timeout=2)
                    self.gui_callback("log", "✅ FFmpeg process force killed")
                except subprocess.TimeoutExpired:
                    # Process is really stuck, try OS-level kill
                    if pid and os.name == 'nt':
                        try:
                            subprocess.run(['taskkill', '/f', '/pid', str(pid)],
                                         capture_output=True, timeout=5)
                            self.gui_callback("log", "🔧 Used taskkill to force terminate process")
                        except:
                            pass

                # Additional wait to ensure cleanup
                time.sleep(1.0)

                # Final verification
                if process.poll() is None:
                    self.gui_callback("log", "⚠️ Warning: FFmpeg process may still be running")

        except Exception as e:
            self.gui_callback("log", f"⚠️ Error during FFmpeg process cleanup: {e}")

    def _cleanup_temp_file(self, file_path):
        """Safely clean up temporary files"""
        try:
            if file_path and os.path.exists(file_path):
                os.remove(file_path)
                self.gui_callback("log", f"🗑️ Cleaned up temporary file: {os.path.basename(file_path)}")
        except Exception as e:
            self.gui_callback("log", f"⚠️ Could not clean up temporary file: {e}")

    def merge_audio_video(self, video_path, audio_path, output_path):
        """Merge separate audio and video files into one MKV file"""
        ffmpeg_process = None
        try:
            self.gui_callback("log", "🎬 Starting audio + video merge...")
            self.gui_callback("log", f"📹 Video: {os.path.basename(video_path)}")
            self.gui_callback("log", f"🔊 Audio: {os.path.basename(audio_path)}")

            # Kill any existing FFmpeg processes first
            self._kill_all_ffmpeg_processes()
            import time
            time.sleep(1.0)

            # FFmpeg command to merge audio and video
            cmd = [
                "ffmpeg",
                "-i", video_path,           # Input video
                "-i", audio_path,           # Input audio
                "-c:v", "copy",             # Copy video stream (no re-encoding)
                "-c:a", "aac",              # Convert audio to AAC (compatible)
                "-map", "0:v:0",            # Map first video stream
                "-map", "1:a:0",            # Map first audio stream
                "-shortest",                # End when shortest stream ends
                "-avoid_negative_ts", "make_zero",  # Fix timestamp issues
                "-fflags", "+genpts",       # Generate presentation timestamps
                "-movflags", "+faststart",  # Optimize for streaming
                "-y",                       # Overwrite output file
                output_path
            ]

            # Hide console window on Windows with enhanced process isolation
            startupinfo = None
            creation_flags = 0
            if os.name == 'nt':
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                creation_flags = subprocess.CREATE_NEW_PROCESS_GROUP | subprocess.CREATE_BREAKAWAY_FROM_JOB

            # Run FFmpeg merge
            ffmpeg_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                stdin=subprocess.PIPE,
                startupinfo=startupinfo,
                creationflags=creation_flags
            )

            # Wait for completion with timeout
            try:
                stdout, stderr = ffmpeg_process.communicate(timeout=300)  # 5 minute timeout
                return_code = ffmpeg_process.returncode
                self.gui_callback("log", f"🔧 FFmpeg merge completed with exit code: {return_code}")
            except subprocess.TimeoutExpired:
                self.gui_callback("log", "⚠️ FFmpeg merge timed out - force terminating process")
                self._force_kill_ffmpeg_process(ffmpeg_process)
                return False
            except Exception as e:
                self.gui_callback("log", f"⚠️ FFmpeg merge error: {e}")
                self._force_kill_ffmpeg_process(ffmpeg_process)
                return False

            # Ensure process is fully terminated
            self._force_kill_ffmpeg_process(ffmpeg_process)
            self._kill_all_ffmpeg_processes()

            # Verify processes are terminated
            self._verify_ffmpeg_processes_terminated()

            # Extended wait for complete cleanup
            self.gui_callback("log", "⏳ Waiting for file handles to be released...")
            time.sleep(5.0)  # Longer wait for complete cleanup

            if return_code == 0 and os.path.exists(output_path):
                if os.path.getsize(output_path) > 1024:
                    self.gui_callback("log", "✅ Audio + Video merge successful!")
                    self.gui_callback("log", f"📁 Output file: {os.path.basename(output_path)}")
                    return True
                else:
                    self.gui_callback("log", "❌ Merge produced empty file")
                    self._cleanup_temp_file(output_path)
                    return False
            else:
                self.gui_callback("log", f"❌ Merge failed with exit code {return_code}")
                self._cleanup_temp_file(output_path)
                return False

        except FileNotFoundError:
            self.gui_callback("log", "⚠️ FFmpeg not found - install FFmpeg for audio/video merging")
            return False
        except Exception as e:
            self.gui_callback("log", f"❌ Error during merge: {e}")
            if ffmpeg_process:
                self._force_kill_ffmpeg_process(ffmpeg_process)
            self._cleanup_temp_file(output_path)
            return False
        finally:
            # Final cleanup
            self._kill_all_ffmpeg_processes()
            time.sleep(1.0)

    def _kill_all_ffmpeg_processes(self):
        """Kill any remaining FFmpeg processes that might be holding file handles"""
        import time

        try:
            if os.name == 'nt':  # Windows
                # Multiple aggressive attempts to kill FFmpeg processes
                commands_to_try = [
                    ['taskkill', '/f', '/im', 'ffmpeg.exe'],
                    ['taskkill', '/f', '/fi', 'IMAGENAME eq ffmpeg*'],
                    ['wmic', 'process', 'where', 'name="ffmpeg.exe"', 'delete']
                ]

                killed_any = False
                for cmd in commands_to_try:
                    try:
                        result = subprocess.run(cmd, capture_output=True, timeout=10)
                        if result.returncode == 0:
                            killed_any = True
                    except:
                        continue  # Try next command

                if killed_any:
                    self.gui_callback("log", "🔧 Killed remaining FFmpeg processes")
                    # Wait for processes to actually terminate
                    time.sleep(2.0)

                # Verify no FFmpeg processes remain
                try:
                    result = subprocess.run(['tasklist', '/fi', 'IMAGENAME eq ffmpeg.exe'],
                                          capture_output=True, timeout=5)
                    if b'ffmpeg.exe' in result.stdout:
                        self.gui_callback("log", "⚠️ Warning: Some FFmpeg processes may still be running")
                except:
                    pass

            else:  # Unix-like
                result = subprocess.run(['pkill', '-9', '-f', 'ffmpeg'],  # Use SIGKILL (-9)
                                      capture_output=True, timeout=5)
                if result.returncode == 0:
                    self.gui_callback("log", "🔧 Killed remaining FFmpeg processes")
                    time.sleep(1.0)

        except Exception as e:
            # Log the error but don't fail the operation
            self.gui_callback("log", f"⚠️ Note: Could not kill FFmpeg processes: {e}")

    def _wait_for_file_release(self, file_path, max_wait=30):
        """Wait for a file to be released by checking if we can open it exclusively"""
        import time

        for i in range(max_wait):
            try:
                # Try to open the file in exclusive mode and immediately close it
                # Use a more conservative approach that doesn't keep the file open
                with open(file_path, 'r+b') as f:
                    # Just try to read one byte to verify access, then close immediately
                    f.seek(0)
                    f.read(1)
                # If we get here, the file is accessible
                return True
            except (PermissionError, OSError, IOError):
                # File is still locked, wait a bit
                time.sleep(1.0)
                if i % 5 == 0:  # Log every 5 seconds
                    self.gui_callback("log", f"⏳ Waiting for file to be released... ({i+1}s)")

        return False

    def _verify_ffmpeg_processes_terminated(self):
        """Verify that all FFmpeg processes have been terminated"""
        try:
            if os.name == 'nt':  # Windows
                result = subprocess.run(['tasklist', '/fi', 'IMAGENAME eq ffmpeg.exe'],
                                      capture_output=True, timeout=5)
                if b'ffmpeg.exe' in result.stdout:
                    self.gui_callback("log", "⚠️ Warning: FFmpeg processes still running after cleanup")
                    return False
                else:
                    self.gui_callback("log", "✅ Verified: No FFmpeg processes running")
                    return True
            else:  # Unix-like
                result = subprocess.run(['pgrep', '-f', 'ffmpeg'],
                                      capture_output=True, timeout=5)
                if result.returncode == 0:
                    self.gui_callback("log", "⚠️ Warning: FFmpeg processes still running after cleanup")
                    return False
                else:
                    self.gui_callback("log", "✅ Verified: No FFmpeg processes running")
                    return True
        except Exception as e:
            self.gui_callback("log", f"⚠️ Could not verify FFmpeg process status: {e}")
            return False

class ProgressTracker:
    """Simple progress tracking with ETA"""

    def __init__(self):
        self.reset()

    def reset(self):
        self.current_percent = 0
        self.start_time = time.time()
        self.progress_history = []

    def update_progress(self, percent):
        self.current_percent = min(100, max(0, percent))
        current_time = time.time()
        self.progress_history.append((current_time, percent))

        # Keep only recent history (last 30 seconds)
        cutoff_time = current_time - 30
        self.progress_history = [(t, p) for t, p in self.progress_history if t > cutoff_time]

    def get_progress_text(self):
        elapsed = int(time.time() - self.start_time)
        elapsed_str = f"{elapsed//60:02d}:{elapsed%60:02d}"
        return f"{self.current_percent:.1f}% | Elapsed: {elapsed_str}"

    def get_eta_text(self):
        """Calculate and return estimated time remaining"""
        if self.current_percent <= 0 or self.current_percent >= 100:
            return "ETA: --:--"

        if len(self.progress_history) < 2:
            return "ETA: Calculating..."

        try:
            oldest_time, oldest_percent = self.progress_history[0]
            newest_time, newest_percent = self.progress_history[-1]

            time_diff = newest_time - oldest_time
            progress_diff = newest_percent - oldest_percent

            if time_diff > 0 and progress_diff > 0:
                speed = progress_diff / time_diff
                remaining_percent = 100 - self.current_percent
                eta_seconds = int(remaining_percent / speed)

                if eta_seconds > 3600:
                    hours = eta_seconds // 3600
                    minutes = (eta_seconds % 3600) // 60
                    return f"ETA: {hours}h {minutes:02d}m"
                else:
                    minutes = eta_seconds // 60
                    seconds = eta_seconds % 60
                    return f"ETA: {minutes:02d}:{seconds:02d}"
            else:
                return "ETA: Calculating..."
        except:
            return "ETA: --:--"

class EasyYTDLP:
    """Simplified GUI application"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("EasyYTDLP - Simplified YouTube Downloader")
        self.root.geometry("900x600")
        self.root.resizable(True, True)

        # Initialize components
        self.progress_tracker = ProgressTracker()
        self.download_manager = DownloadManager(self._handle_callback)

        # Variables
        self.url_var = tk.StringVar()
        self.filename_var = tk.StringVar()
        self.output_dir_var = tk.StringVar(value=os.getcwd())
        self.quality_var = tk.StringVar(value="1080p")
        self.audio_only_var = tk.BooleanVar()

        # Set up cleanup on application exit
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)

        self._create_gui()
        self._update_button_states()

    def _create_gui(self):
        """Create the simplified GUI"""
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky="nsew")

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(0, weight=1)

        # Left side - Controls
        left_frame = ttk.Frame(main_frame, padding="5")
        left_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 10))
        left_frame.columnconfigure(0, weight=1)

        # Right side - Log
        right_frame = ttk.Frame(main_frame, padding="5")
        right_frame.grid(row=0, column=1, sticky="nsew")
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(1, weight=1)

        # === LEFT SIDE - CONTROLS ===
        row = 0

        # Title
        title_label = ttk.Label(left_frame, text="EasyYTDLP v2.1",
                               font=("Arial", 16, "bold"))
        title_label.grid(row=row, column=0, pady=(0, 10))
        row += 1

        # URL input
        ttk.Label(left_frame, text="YouTube URL:", font=("Arial", 9, "bold")).grid(
            row=row, column=0, sticky="w", pady=2)
        row += 1
        url_entry = ttk.Entry(left_frame, textvariable=self.url_var, width=50, font=("Arial", 9))
        url_entry.grid(row=row, column=0, sticky="ew", pady=(0, 5))
        row += 1

        # Filename input
        ttk.Label(left_frame, text="Filename:", font=("Arial", 9, "bold")).grid(
            row=row, column=0, sticky="w", pady=2)
        row += 1
        filename_frame = ttk.Frame(left_frame)
        filename_frame.grid(row=row, column=0, sticky="ew", pady=(0, 5))
        filename_frame.columnconfigure(0, weight=1)
        filename_entry = ttk.Entry(filename_frame, textvariable=self.filename_var, font=("Arial", 9))
        filename_entry.grid(row=0, column=0, sticky="ew", padx=(0, 5))
        ttk.Button(filename_frame, text="Auto", command=self._auto_fill_filename).grid(
            row=0, column=1)
        row += 1

        # Output directory
        ttk.Label(left_frame, text="Output Directory:", font=("Arial", 9, "bold")).grid(
            row=row, column=0, sticky="w", pady=2)
        row += 1
        dir_frame = ttk.Frame(left_frame)
        dir_frame.grid(row=row, column=0, sticky="ew", pady=(0, 10))
        dir_frame.columnconfigure(0, weight=1)
        dir_entry = ttk.Entry(dir_frame, textvariable=self.output_dir_var, font=("Arial", 9))
        dir_entry.grid(row=0, column=0, sticky="ew", padx=(0, 5))
        ttk.Button(dir_frame, text="Browse", command=self._browse_directory).grid(
            row=0, column=1)
        row += 1

        # Options frame
        options_frame = ttk.LabelFrame(left_frame, text="Options", padding="8")
        options_frame.grid(row=row, column=0, sticky="ew", pady=(0, 10))
        options_frame.columnconfigure(0, weight=1)
        row += 1

        # Quality selection
        quality_frame = ttk.Frame(options_frame)
        quality_frame.grid(row=0, column=0, sticky="ew", pady=2)
        quality_frame.columnconfigure(1, weight=1)

        ttk.Label(quality_frame, text="Quality:", font=("Arial", 9)).grid(
            row=0, column=0, sticky="w", padx=(0, 5))
        quality_combo = ttk.Combobox(quality_frame, textvariable=self.quality_var,
                                   values=["Best Available", "1080p", "720p", "480p"],
                                   state="readonly", width=12, font=("Arial", 9))
        quality_combo.grid(row=0, column=1, sticky="w")

        # Audio only checkbox
        ttk.Checkbutton(options_frame, text="Audio Only (MP3)",
                       variable=self.audio_only_var).grid(
            row=1, column=0, sticky="w", pady=2)

        # Live stream options
        stream_frame = ttk.LabelFrame(left_frame, text="Live Stream Mode", padding="8")
        stream_frame.grid(row=row, column=0, sticky="ew", pady=(0, 10))
        row += 1

        self.stream_start_var = tk.StringVar(value="from_now")

        ttk.Radiobutton(stream_frame, text="📺 From Now",
                       variable=self.stream_start_var, value="from_now").grid(
            row=0, column=0, sticky="w", pady=1)

        ttk.Radiobutton(stream_frame, text="⏮️ From Start",
                       variable=self.stream_start_var, value="from_start").grid(
            row=1, column=0, sticky="w", pady=1)

        # Simple mode for problematic streams
        self.simple_mode_var = tk.BooleanVar()
        ttk.Checkbutton(stream_frame, text="🔧 Simple Mode (for problematic streams)",
                       variable=self.simple_mode_var).grid(
            row=2, column=0, sticky="w", pady=1)

        # Control buttons
        button_frame = ttk.Frame(left_frame)
        button_frame.grid(row=row, column=0, pady=(0, 10))
        row += 1

        self.download_btn = ttk.Button(button_frame, text="🚀 Start",
                                     command=self._start_download)
        self.download_btn.grid(row=0, column=0, padx=(0, 5), pady=2, sticky="ew")

        self.finish_btn = ttk.Button(button_frame, text="⏹️ Finish",
                                   command=self._finish_here)
        self.finish_btn.grid(row=0, column=1, padx=5, pady=2, sticky="ew")

        self.stop_btn = ttk.Button(button_frame, text="🛑 Stop",
                                 command=self._stop_download)
        self.stop_btn.grid(row=0, column=2, padx=(5, 0), pady=2, sticky="ew")

        # Second row of buttons
        self.fix_btn = ttk.Button(button_frame, text="🔧 Fix Video",
                                command=self._fix_video_manual)
        self.fix_btn.grid(row=1, column=0, columnspan=1, pady=(5, 0), sticky="ew")

        self.merge_btn = ttk.Button(button_frame, text="🎬 Merge A+V",
                                  command=self._merge_audio_video_manual)
        self.merge_btn.grid(row=1, column=1, columnspan=2, pady=(5, 0), sticky="ew", padx=(5, 0))

        # Configure button frame columns
        button_frame.columnconfigure(0, weight=1)
        button_frame.columnconfigure(1, weight=1)
        button_frame.columnconfigure(2, weight=1)

        # About section
        about_frame = ttk.LabelFrame(left_frame, text="About", padding="8")
        about_frame.grid(row=row, column=0, sticky="ew")

        about_text = ttk.Label(about_frame,
                              text="EasyYTDLP v2.0\nSimplified & Optimized\nCreated by Min\n2025/06/20",
                              font=("Arial", 8), foreground="gray", justify="center")
        about_text.grid(row=0, column=0, sticky="ew")
        about_frame.columnconfigure(0, weight=1)

        # === RIGHT SIDE - PROGRESS AND LOG ===

        # Progress section
        progress_frame = ttk.LabelFrame(right_frame, text="Progress", padding="10")
        progress_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=0, column=0, sticky="ew", pady=(0, 5))

        # Progress info frame
        progress_info_frame = ttk.Frame(progress_frame)
        progress_info_frame.grid(row=1, column=0, sticky="ew")
        progress_info_frame.columnconfigure(0, weight=1)
        progress_info_frame.columnconfigure(1, weight=1)

        # Progress text and ETA
        self.progress_label = ttk.Label(progress_info_frame, text="Ready to download", font=("Arial", 9))
        self.progress_label.grid(row=0, column=0, sticky="w")

        self.eta_label = ttk.Label(progress_info_frame, text="ETA: --:--", font=("Arial", 9))
        self.eta_label.grid(row=0, column=1, sticky="e")

        # Log section
        log_frame = ttk.LabelFrame(right_frame, text="Download Log", padding="10")
        log_frame.grid(row=1, column=0, sticky="nsew")
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        # Log text with scrollbar
        log_container = ttk.Frame(log_frame)
        log_container.grid(row=0, column=0, sticky="nsew")
        log_container.columnconfigure(0, weight=1)
        log_container.rowconfigure(0, weight=1)

        self.log_text = tk.Text(log_container, wrap="word", height=15, font=("Consolas", 9))
        self.log_text.grid(row=0, column=0, sticky="nsew")

        log_scrollbar = ttk.Scrollbar(log_container, orient="vertical", command=self.log_text.yview)
        log_scrollbar.grid(row=0, column=1, sticky="ns")
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

    def _handle_callback(self, callback_type, data):
        """Handle callbacks from download manager (thread-safe)"""
        if callback_type == "log":
            self._log_message(data)
        elif callback_type == "progress":
            self._update_progress(data)
        elif callback_type == "download_finished":
            self._download_finished(data)

    def _log_message(self, message):
        """Add message to log (thread-safe)"""
        def update():
            self.log_text.insert("end", f"{message}\n")
            self.log_text.see("end")
        self.root.after(0, update)

    def _update_progress(self, percent):
        """Update progress bar and ETA (thread-safe)"""
        def update():
            self.progress_tracker.update_progress(percent)
            self.progress_var.set(percent)
            self.progress_label.config(text=self.progress_tracker.get_progress_text())
            self.eta_label.config(text=self.progress_tracker.get_eta_text())
        self.root.after(0, update)

    def _download_finished(self, status):
        """Handle download completion (thread-safe)"""
        def update():
            self._update_button_states()
            if status == "success":
                self.progress_label.config(text="✅ Download completed successfully!")
                messagebox.showinfo("Success", "Download completed successfully!")
            elif status == "finished_here":
                self.progress_label.config(text="⏹️ Download finished at current point")
                messagebox.showinfo("Finished Here", "Download finished at current point!\nPartial file saved.")
            elif status == "cancelled":
                self.progress_label.config(text="🛑 Download stopped by user")
            elif status == "failed":
                self.progress_label.config(text="❌ Download failed")
                messagebox.showerror("Error", "Download failed. Check the log for details.")
            elif status == "error":
                self.progress_label.config(text="❌ Download error occurred")
                messagebox.showerror("Error", "An error occurred during download.")

            # Reset progress bar and ETA
            self.progress_var.set(0)
            self.eta_label.config(text="")

        self.root.after(0, update)

    def _update_button_states(self):
        """Update button states based on download status"""
        is_downloading = self.download_manager.is_downloading

        self.download_btn.config(state="disabled" if is_downloading else "normal")
        self.finish_btn.config(state="normal" if is_downloading else "disabled")
        self.stop_btn.config(state="normal" if is_downloading else "disabled")

    def _auto_fill_filename(self):
        """Auto-fill filename based on current timestamp"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if self.audio_only_var.get():
            filename = f"audio_{timestamp}"
        else:
            filename = f"video_{timestamp}"
        self.filename_var.set(filename)

    def _browse_directory(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(initialdir=self.output_dir_var.get())
        if directory:
            self.output_dir_var.set(directory)

    def _start_download(self):
        """Start the download process"""
        # Validate inputs
        url = self.url_var.get().strip()
        filename = self.filename_var.get().strip()
        output_dir = self.output_dir_var.get().strip()

        if not url:
            messagebox.showerror("Error", "Please enter a YouTube URL")
            return

        if not filename:
            messagebox.showerror("Error", "Please enter a filename")
            return

        if not os.path.exists(output_dir):
            messagebox.showerror("Error", "Output directory does not exist")
            return

        # Reset progress
        self.progress_tracker.reset()
        self.progress_var.set(0)
        self.progress_label.config(text="Starting download...")
        self.eta_label.config(text="ETA: --:--")

        # Clear log
        self.log_text.delete(1.0, "end")

        # Get options
        quality = self.quality_var.get()
        audio_only = self.audio_only_var.get()
        stream_start = self.stream_start_var.get()
        simple_mode = self.simple_mode_var.get()

        # Start download
        success = self.download_manager.start_download(
            url=url,
            filename=filename,
            output_dir=output_dir,
            quality=quality,
            audio_only=audio_only,
            stream_start=stream_start,
            simple_mode=simple_mode
        )

        if success:
            self._update_button_states()
            mode = "Audio" if audio_only else f"Video ({quality})"
            self._log_message(f"🚀 Starting {mode} download...")
            self._log_message(f"📁 Output: {os.path.join(output_dir, filename)}")
        else:
            messagebox.showerror("Error", "Failed to start download")

    def _finish_here(self):
        """Finish download at current point"""
        if not self.download_manager.is_downloading:
            return

        result = messagebox.askyesno("Finish Here",
                                   "🎬 Finish download at current point?\n\n"
                                   "This will stop the download and save the partial file.\n"
                                   "Perfect for live streams and long videos!\n\n"
                                   "Continue?",
                                   icon="question")
        if result:
            self.download_manager.finish_here()
            self._update_button_states()

    def _stop_download(self):
        """Stop the current download"""
        if not self.download_manager.is_downloading:
            return

        result = messagebox.askyesno("Stop Download",
                                   "⚠️ WARNING: Stopping download before completion!\n\n"
                                   "The downloaded file may be corrupted or incomplete.\n"
                                   "Are you sure you want to stop the download?",
                                   icon="warning")
        if result:
            self.download_manager.stop_download()
            self._update_button_states()

    def _fix_video_manual(self):
        """Manually fix a corrupted video file"""
        # Ask user to select a video file
        file_path = filedialog.askopenfilename(
            title="Select corrupted video file to fix",
            filetypes=[
                ("Video files", "*.mkv *.mp4 *.webm *.avi *.mov"),
                ("All files", "*.*")
            ],
            initialdir=self.output_dir_var.get()
        )

        if not file_path:
            return

        # Clear log and show progress
        self.log_text.delete(1.0, "end")
        self._log_message(f"🔧 Fixing video file: {os.path.basename(file_path)}")
        self._log_message("⏳ This may take a few moments...")

        # Create output path (temporary name)
        base_name = os.path.splitext(file_path)[0]
        temp_fixed_path = f"{base_name}_temp_fixed.mkv"

        # Run fix in separate thread to avoid freezing GUI
        def fix_thread():
            try:
                # Create a temporary download manager for the fix
                temp_manager = DownloadManager(self._handle_callback)

                # Note: _fix_corrupted_video now handles file replacement internally
                if temp_manager._fix_corrupted_video(file_path, temp_fixed_path):
                    self._log_message("✅ Video fix completed successfully!")
                    self._log_message(f"📁 Fixed file: {os.path.basename(file_path)}")

                    def show_success():
                        messagebox.showinfo("Success",
                            f"Video file fixed successfully!\n\n"
                            f"File: {os.path.basename(file_path)}\n\n"
                            f"The corrupted file has been replaced with the fixed version.")

                    self.root.after(0, show_success)
                else:
                    self._log_message("❌ Video fix failed")

                    def show_error():
                        messagebox.showerror("Fix Failed",
                            "Could not fix the video file.\n\n"
                            "Possible reasons:\n"
                            "• File is too corrupted\n"
                            "• FFmpeg is not installed\n"
                            "• File is still being used by another process")

                    self.root.after(0, show_error)

            except Exception as e:
                self._log_message(f"❌ Error during fix: {e}")

                def show_exception():
                    messagebox.showerror("Error", f"An error occurred while fixing the video:\n{e}")

                self.root.after(0, show_exception)

        # Start fix thread
        import threading
        thread = threading.Thread(target=fix_thread, daemon=True)
        thread.start()

    def _merge_audio_video_manual(self):
        """Manually merge separate audio and video files"""
        # Ask user to select video file
        video_path = filedialog.askopenfilename(
            title="Select video file",
            filetypes=[
                ("Video files", "*.mkv *.mp4 *.webm *.avi *.mov *.flv"),
                ("All files", "*.*")
            ],
            initialdir=self.output_dir_var.get()
        )

        if not video_path:
            return

        # Ask user to select audio file
        audio_path = filedialog.askopenfilename(
            title="Select audio file",
            filetypes=[
                ("Audio files", "*.mp3 *.m4a *.aac *.wav *.flac *.ogg *.opus"),
                ("All files", "*.*")
            ],
            initialdir=os.path.dirname(video_path)
        )

        if not audio_path:
            return

        # Ask user where to save the merged file
        output_path = filedialog.asksaveasfilename(
            title="Save merged file as",
            defaultextension=".mkv",
            filetypes=[
                ("MKV files", "*.mkv"),
                ("MP4 files", "*.mp4"),
                ("All files", "*.*")
            ],
            initialdir=os.path.dirname(video_path),
            initialfile=f"{os.path.splitext(os.path.basename(video_path))[0]}_merged.mkv"
        )

        if not output_path:
            return

        # Clear log and show progress
        self.log_text.delete(1.0, "end")
        self._log_message(f"🎬 Merging audio and video files...")
        self._log_message(f"📹 Video: {os.path.basename(video_path)}")
        self._log_message(f"🔊 Audio: {os.path.basename(audio_path)}")
        self._log_message(f"📁 Output: {os.path.basename(output_path)}")
        self._log_message("⏳ This may take a few moments...")

        # Run merge in separate thread to avoid freezing GUI
        def merge_thread():
            try:
                # Create a temporary download manager for the merge
                temp_manager = DownloadManager(self._handle_callback)

                if temp_manager.merge_audio_video(video_path, audio_path, output_path):
                    self._log_message("✅ Audio + Video merge completed successfully!")
                    self._log_message(f"📁 Merged file: {os.path.basename(output_path)}")

                    def show_success():
                        messagebox.showinfo("Merge Successful",
                            f"Audio and video merged successfully!\n\n"
                            f"Video: {os.path.basename(video_path)}\n"
                            f"Audio: {os.path.basename(audio_path)}\n"
                            f"Output: {os.path.basename(output_path)}\n\n"
                            f"The merged file is ready to play!")

                    self.root.after(0, show_success)
                else:
                    self._log_message("❌ Audio + Video merge failed!")
                    self._log_message("💡 Check that both files are valid and FFmpeg is installed.")

                    def show_error():
                        messagebox.showerror("Merge Failed",
                            f"Could not merge the audio and video files.\n\n"
                            f"Video: {os.path.basename(video_path)}\n"
                            f"Audio: {os.path.basename(audio_path)}\n\n"
                            f"Please ensure both files are valid and FFmpeg is installed.")

                    self.root.after(0, show_error)

            except Exception as e:
                self._log_message(f"❌ Error during merge: {e}")

                def show_error():
                    messagebox.showerror("Error", f"An error occurred during merging:\n\n{e}")

                self.root.after(0, show_error)

        # Start merge in background thread
        import threading
        thread = threading.Thread(target=merge_thread, daemon=True)
        thread.start()

    def _on_closing(self):
        """Handle application closing with proper cleanup"""
        try:
            # Stop any active downloads
            if self.download_manager.is_downloading:
                self.download_manager.stop_download()

            # Kill any remaining FFmpeg processes
            self.download_manager._kill_all_ffmpeg_processes()

            # Brief wait for cleanup to complete
            import time
            time.sleep(0.5)

        except Exception as e:
            print(f"Error during cleanup: {e}")
        finally:
            # Close the application
            self.root.destroy()

    def run(self):
        """Start the GUI application"""
        self.root.mainloop()

def main():
    """Main entry point"""
    print("Starting EasyYTDLP v2.0 - Simplified YouTube Downloader")

    # Check for yt-dlp.exe
    if getattr(sys, 'frozen', False):
        script_dir = os.path.dirname(sys.executable)
    else:
        script_dir = os.path.dirname(os.path.abspath(__file__))

    yt_path = os.path.join(script_dir, "yt-dlp.exe")

    if not os.path.exists(yt_path):
        print(f"❌ Error: yt-dlp.exe not found at {yt_path}")
        print("Please ensure yt-dlp.exe is in the same directory as this script.")
        input("Press Enter to exit...")
        return

    # Start GUI
    try:
        app = EasyYTDLP()
        app.run()
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()