@echo off
title EasyYTDLP v1.1.0 - Optimal YouTube Downloader
color 0A
echo.
echo ========================================
echo  EasyYTDLP v1.1.0 - YouTube Downloader
echo  Optimal Commands with Live Stream Support
echo ========================================
echo.

REM Check if Python is available
echo [1/3] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo.
    echo Please install Python 3.9+ from: https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
) else (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do echo [OK] Python %%i detected
)

REM Check if yt-dlp.exe exists
echo [2/3] Checking yt-dlp.exe...
if not exist "yt-dlp.exe" (
    echo [ERROR] yt-dlp.exe not found in current directory
    echo.
    echo Please download yt-dlp.exe from:
    echo https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp.exe
    echo.
    echo Place it in the same folder as this batch file and main.py
    echo.
    pause
    exit /b 1
) else (
    echo [OK] yt-dlp.exe found
)

REM Check if FFmpeg is available (optional but recommended)
echo [3/3] Checking FFmpeg (optional for live stream merging)...
ffmpeg -version >nul 2>&1
if errorlevel 1 (
    echo [WARNING] FFmpeg not found - live stream merging will be limited
    echo [INFO] Download FFmpeg from: https://ffmpeg.org/download.html
) else (
    echo [OK] FFmpeg detected - full live stream support available
)

echo.
echo [STARTING] Launching EasyYTDLP GUI...
echo.

REM Run the application
python main.py

REM Keep window open if there's an error
if errorlevel 1 (
    echo.
    echo [ERROR] Application exited with an error code.
    echo Check the error messages above for details.
    echo.
    pause
) else (
    echo.
    echo [SUCCESS] EasyYTDLP closed normally.
)
