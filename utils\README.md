# EasyYTDLP Utilities

This directory contains utility scripts for EasyYTDLP development and distribution.

## Files

### `build_exe.py`
Creates a standalone executable using PyInstaller.

**Usage:**
```bash
cd EasyYTDLP
python utils/build_exe.py
```

**Requirements:**
- PyInstaller: `pip install pyinstaller`
- yt-dlp.exe must be present in the root directory

**Output:**
- Creates `dist/EasyYTDLP.exe` - standalone executable
- Includes yt-dlp.exe bundled within the executable

### `download_ytdlp.py`
Downloads the latest yt-dlp.exe from GitHub releases.

**Usage:**
```bash
cd EasyYTDLP
python utils/download_ytdlp.py
```

**Features:**
- Downloads latest stable release
- Verifies download integrity
- Places yt-dlp.exe in the root directory

## Building Distribution

1. Download yt-dlp.exe:
   ```bash
   python utils/download_ytdlp.py
   ```

2. Build executable:
   ```bash
   python utils/build_exe.py
   ```

3. Distribute the `dist/` folder contents
