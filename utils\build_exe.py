#!/usr/bin/env python3
"""
Build script for EasyYTDLP executable
Creates a standalone executable using PyInstaller
"""

import os
import sys
import subprocess
import shutil

def check_requirements():
    """Check if PyInstaller is available"""
    try:
        import PyInstaller
        print("✅ PyInstaller found")
        return True
    except ImportError:
        print("❌ PyInstaller not found")
        print("Install with: pip install pyinstaller")
        return False

def build_executable():
    """Build the executable using PyInstaller"""
    print("🔨 Building EasyYTDLP executable...")

    # Change to parent directory for build
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    os.chdir(parent_dir)

    # PyInstaller command
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=EasyYTDLP",
        "--icon=icon.ico" if os.path.exists("icon.ico") else "",
        "--add-data=yt-dlp.exe;.",
        "--distpath=dist",
        "--workpath=build",
        "main.py"
    ]
    
    # Remove empty icon parameter if no icon file
    cmd = [arg for arg in cmd if arg]
    
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ Build completed successfully!")
        print("📁 Executable created: dist/EasyYTDLP.exe")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        return False

def copy_files():
    """Copy necessary files to dist folder"""
    if not os.path.exists("dist"):
        return
    
    files_to_copy = [
        "README.md",
        "yt-dlp.exe"
    ]
    
    for file in files_to_copy:
        if os.path.exists(file):
            shutil.copy2(file, "dist/")
            print(f"📋 Copied {file} to dist/")

def main():
    """Main build process"""
    print("🚀 EasyYTDLP Build Script")
    print("=" * 40)
    
    # Check requirements
    if not check_requirements():
        return 1
    
    # Check if yt-dlp.exe exists
    if not os.path.exists("yt-dlp.exe"):
        print("❌ yt-dlp.exe not found")
        print("Download it first with: python utils/download_ytdlp.py")
        return 1
    
    # Build executable
    if not build_executable():
        return 1
    
    # Copy additional files
    copy_files()
    
    print("\n✅ Build process completed!")
    print("📦 Check the 'dist' folder for your executable")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
