#!/usr/bin/env python3
"""
Download script for yt-dlp.exe
Downloads the latest yt-dlp executable from GitHub
"""

import os
import sys
import urllib.request
import urllib.error

def download_ytdlp():
    """Download the latest yt-dlp.exe from GitHub"""
    url = "https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp.exe"
    filename = "yt-dlp.exe"
    
    print("📥 Downloading yt-dlp.exe...")
    print(f"🔗 URL: {url}")
    
    try:
        # Download with progress
        def progress_hook(block_num, block_size, total_size):
            if total_size > 0:
                percent = min(100, (block_num * block_size * 100) // total_size)
                print(f"\r⏳ Progress: {percent}%", end="", flush=True)
        
        urllib.request.urlretrieve(url, filename, progress_hook)
        print(f"\n✅ Downloaded successfully: {filename}")
        
        # Check file size
        size = os.path.getsize(filename) / (1024 * 1024)
        print(f"📊 File size: {size:.1f} MB")
        
        return True
        
    except urllib.error.URLError as e:
        print(f"\n❌ Download failed: {e}")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return False

def main():
    """Main download process"""
    print("🚀 EasyYTDLP - yt-dlp.exe Downloader")
    print("=" * 40)
    
    # Check if file already exists
    if os.path.exists("yt-dlp.exe"):
        response = input("yt-dlp.exe already exists. Overwrite? (y/N): ")
        if response.lower() not in ['y', 'yes']:
            print("❌ Download cancelled")
            return 1
    
    # Download
    if download_ytdlp():
        print("\n🎉 yt-dlp.exe is ready!")
        print("You can now run: python main.py")
        return 0
    else:
        print("\n❌ Download failed")
        print("Please download manually from:")
        print("https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp.exe")
        return 1

if __name__ == "__main__":
    sys.exit(main())
